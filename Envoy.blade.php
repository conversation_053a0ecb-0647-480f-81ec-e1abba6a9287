@servers(['production' => '<EMAIL>','staging' => 'ssm-user@127.0.0.1 -p 8888 -i ~/.ssh/thankview-qa-ec2.pem  -o StrictHostKeyChecking=no'])



@setup
    $project_name = 'ThankView Envelope Builder';
@endsetup

@task('deploy-staging', ['on' => 'staging'])
	cd /var/www/builders
	sudo chown -R ssm-user:www-data .
    sudo chmod -R 644 .
    sudo chmod -R 775 .
	git stash
	git pull
	composer dump-autoload
	php artisan cache:clear
	php artisan migrate
	php artisan db:seed
@endtask

@task('deploy-builder-prod', ['on' => ['production'], 'confirm' => true])
	cd /var/www/ThankView-Envelope-Builder
	git pull
	composer dump-autoload
@endtask


@after
   @slack('*******************************************************************************', '#devalerts')
@endafter
