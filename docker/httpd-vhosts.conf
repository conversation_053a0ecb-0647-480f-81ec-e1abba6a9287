
<VirtualHost *:80>
    ServerName builder-thankview.com
    ServerAlias builder-thankview.com
    Redirect permanent / https://builder-thankview.com:4432/
</VirtualHost>

<VirtualHost *:443>
    ServerName builder-thankview.com
    ServerAdmin <EMAIL>
    DocumentRoot /var/www/html/public
    SSLEngine on

    SSLCertificateFile /etc/apache2/ssl/server.crt
    SSLCertificateKeyFile /etc/apache2/ssl/server.key

    <Directory "/var/www/html/public">
          Options Indexes FollowSymLinks MultiViews
          AllowOverride All
          Order allow,deny
          allow from all
    </Directory>
</VirtualHost>