{"name": "laravel/lumen", "description": "The Laravel Lumen Framework.", "keywords": ["framework", "laravel", "lumen"], "license": "MIT", "type": "project", "require": {"php": "8.1.*", "aws/aws-sdk-php": "^3.220", "bugsnag/bugsnag-laravel": "^2.0", "laravel/lumen-framework": "^10.0", "laravel/tinker": "^2.4", "league/flysystem": "^3.15", "league/flysystem-aws-s3-v3": "^3.0", "league/mime-type-detection": "^1.7", "sentry/sentry-laravel": "^4.10"}, "require-dev": {"mockery/mockery": "^1.3.1", "phpunit/phpunit": "^8.5", "squizlabs/php_codesniffer": "^3.5"}, "autoload": {"classmap": ["database/seeds", "database/factories"], "files": ["bootstrap/FilesystemHelper.php"], "psr-4": {"App\\": "app/"}}, "autoload-dev": {"classmap": ["tests/"], "psr-4": {"Tests\\": "tests/"}}, "config": {"preferred-install": "dist", "sort-packages": true, "optimize-autoloader": true}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""]}}