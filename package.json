{"private": true, "scripts": {"dev": "npm run development", "development": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch": "cross-env NODE_ENV=development node_modules/webpack/bin/webpack.js --watch --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "watch-poll": "npm run watch -- --watch-poll", "hot": "cross-env NODE_ENV=development node_modules/webpack-dev-server/bin/webpack-dev-server.js --inline --hot --https --cert docker/ssl/server.crt --key docker/ssl/server.key --config=node_modules/laravel-mix/setup/webpack.config.js", "prod": "npm run production", "preproduction": "cross-env NODE_ENV=production MIX_BUILDER_APP_URL=https://builder.thankview.com MIX_THANKVIEW_APP_URL=https://thankview.com node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js", "production": "rsync -av public/build $(grep BUILDER_PROD .env | cut -d '=' -f2)", "postproduction": "npm run dev", "staging": "cross-env NODE_ENV=development MIX_BUILDER_APP_URL=https://builder.stage-thankview.com MIX_THANKVIEW_APP_URL=https://stage-thankview.com node_modules/webpack/bin/webpack.js --progress --config=node_modules/laravel-mix/setup/webpack.config.js"}, "devDependencies": {"@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.4", "cross-env": "^5.2.0", "eslint": "^8.5.0", "eslint-plugin-vue": "^8.2.0", "husky": "^4.2.5", "laravel-mix": "^5.0.4", "laravel-mix-purgecss": "^5.0.0", "resolve-url-loader": "^3.1.0", "sass": "^1.34.0", "sass-loader": "^8.0.2", "vue-template-compiler": "^2.7.16"}, "dependencies": {"@bugsnag/js": "^7.11.0", "@bugsnag/plugin-vue": "^7.11.0", "@evertrue/tv-components": "^0.2.1", "@sentry/tracing": "^7.120.3", "@sentry/vue": "^8.50.0", "a11y-dialog": "^7.3.0", "axios": "^0.19.2", "es6-promise": "^4.2.8", "vue": "^2.6.11", "vue-router": "^3.1.5", "vuelidate": "^0.7.5"}, "husky": {"hooks": {"commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}}