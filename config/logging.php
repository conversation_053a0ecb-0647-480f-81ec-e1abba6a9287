<?php

return [

    'default' => env('LOG_CHANNEL', 'stack'),

    'channels' => [
        'stack' => [
            'driver' => 'stack',
            'channels' => ['syslog', 'bugsnag', 'daily'],
        ],

        'syslog' => [
            'driver' => 'syslog',
            'level' => 'debug',
            'path' => storage_path('logs/testsyslog.log'),
        ],

        'bugsnag' => [
            'driver' => 'bugsnag',
            'level' => 'warning'
        ],

        'daily' => [
            'driver' => 'daily',
            'level' => 'debug',
            'path' => storage_path('logs/laravel.log'),
        ]
    ],
];