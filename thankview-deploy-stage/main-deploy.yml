---
# main-deploy.yml - Main deployment playbook optimized for GitHub Actions
- name: Deploy ThankView Envelope Builder Environment
  hosts: localhost
  connection: local
  gather_facts: false
  vars:
    deploy_env: "stage" # Can be overridden with -e deploy_env=prod
  pre_tasks:
    - name: Display deployment start message
      debug:
        msg: "Starting ThankView Envelope Builder deployment to {{ deploy_env }} environment"

# Only include the builder-deploy.yml playbook since this is the Envelope Builder repository
- import_playbook: builder-deploy.yml
  tags: [builder, all]
  ignore_errors: yes

- name: Send notification
  hosts: localhost
  connection: local
  gather_facts: false
  tags: [notification, always]
  # tasks:
  # - name: Retrieve Slack token from AWS Parameter Store
  # amazon.aws.aws_ssm_parameter_store:
  # names:
  # - /thankview/slack-token
  # region: us-east-1
  # register: slack_params
  # no_log: true
  # - name: Send Slack notification
  # community.general.slack:
  # token: "{{ slack_params.parameters['/thankview/slack-token'] }}"
  # msg: "{{ deploy_env | capitalize }} deployment completed via GitHub Actions!"
  # channel: "#devalerts"
  # failed_when: false # Ensure notification attempts don't cause the playbook to fail