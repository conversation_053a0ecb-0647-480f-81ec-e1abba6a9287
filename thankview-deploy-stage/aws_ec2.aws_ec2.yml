plugin: amazon.aws.aws_ec2
regions:
  - us-east-1
  - us-east-2
  - us-west-1
  - ca-central-1
filters:
  instance-state-name: running
  "tag:Environment": stage
keyed_groups:
  - key: placement.availability_zone
    prefix: az
    separator: ''
  - key: tags.Name
    prefix: ''
    separator: ''
compose:
  ansible_host: private_ip_address
  ansible_user: "{{ 'ubuntu' if 'ubuntu' in tags.OS|default('') else 'ec2-user' if 'amazon' in tags.OS|default('') else 'ssm-user' if 'tv-stage-webserver' in tags.Name|default('') else 'ubuntu' }}"
groups:
  webservers: "tags.Name is defined and 'tv-stage-webserver' in tags.Name"
  video_workers: "tags.Name is defined and 'tv-stage-video' in tags.Name"
  send_servers: "tags.Name is defined and 'tv-stage-sends' in tags.Name"
  stage_web: "tags.Name is defined and 'tv-stage-webserver' in tags.Name"
  stage_media: "tags.Name is defined and 'tv-stage-media' in tags.Name"
  stage_aws_workers: "tags.Name is defined and ('tv-stage-video' in tags.Name or 'tv-stage-sends' in tags.Name)"
  stage_others: "tags.Name is defined and ('scheduler_host' in tags.Name or 'sftp_host' in tags.Name)"
  stage_secure: "tags.Name is defined and 'tv-stage-secure' in tags.Name"
  builder: "tags.Name is defined and 'builder_host' in tags.Name"
  redis: "tags.Name is defined and 'redis_host' in tags.Name"
  stage_api_host: "tags.Name is defined and 'api_host' in tags.Name"