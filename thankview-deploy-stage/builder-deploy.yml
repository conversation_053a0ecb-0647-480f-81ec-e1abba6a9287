---
# builder-deploy.yml - Equivalent of the Envoy deploy-builder-prod task

- name: Deploy to builder servers
  hosts: stage_builder_host
  vars:
    app_path: /var/www/ThankView-Envelope-Builder
    git_repo: "**************:evertrue/ThankView-Envelope-Builder.git"  # Updated with correct repo
    git_branch: "{{ deploy_branch | default('staging') }}"
    user: ubuntu  # Based on the original PHP script

  tasks:
    - name: Ensure .ssh directory exists
      ansible.builtin.file:
        path: "/home/<USER>/.ssh"
        state: directory
        mode: '0700'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true

    - name: Get SSH key using AWS CLI
      ansible.builtin.shell: |
        aws ssm get-parameter --name "/thankview-stage/stage_builder_key" --with-decryption --region us-east-1 --query Parameter.Value --output text
      register: ssh_key_cli
      delegate_to: localhost
      no_log: true

    - name: Save SSH key from CLI to file
      ansible.builtin.copy:
        content: "{{ ssh_key_cli.stdout }}\n"
        dest: "/home/<USER>/.ssh/stage_builder_key"
        mode: '0600'
        owner: "{{ user }}"
        group: "{{ user }}"
      become: true
      no_log: true

    - name: Check if git repo already exists
      ansible.builtin.stat:
        path: "{{ app_path }}/.git"
      register: git_dir

    - name: Clone git repository if it doesn't exist
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        key_file: "/home/<USER>/.ssh/"
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
      when: not git_dir.stat.exists
      become: true
      become_user: "{{ user }}"


    - name: Reset repository to the latest commit
      ansible.builtin.shell:
        cmd: |
          git reset --hard HEAD
          git clean -fd
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Allow Git to work in this repository
      ansible.builtin.shell:
        cmd: git config --global --add safe.directory /var/www/ThankView-Envelope-Builder
      become: true
      become_user: "{{ user }}"


    - name: Pull latest code if repo exists
      ansible.builtin.git:
        repo: "{{ git_repo }}"
        dest: "{{ app_path }}"
        update: yes
        version: "{{ git_branch }}" #Using the staging branch
        key_file: "/home/<USER>/.ssh/stage_builder_key"
        accept_hostkey: yes
        ssh_opts: "-o StrictHostKeyChecking=no"
      when: git_dir.stat.exists
      become: true
      become_user: "{{ user }}"

    - name: Run composer update
      community.general.composer:
        command: update
        working_dir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Save git commit hash
      ansible.builtin.shell: git log -1 --pretty=%h > git_commit
      args:
        chdir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    - name: Run composer dump-autoload
      community.general.composer:
        command: dump-autoload
        working_dir: "{{ app_path }}"
      become: true
      become_user: "{{ user }}"

    # New task to fetch and update .env files from AWS Parameter Store
    - name: Overwrite .env files from SSM via lookup
      block:
        - name: Fetch the full .env blob from Parameter Store
          set_fact:
            env_content: "{{ lookup(
              'aws_ssm',
              '/thankview-stage/builder-env',
              region='us-east-1',
              decrypt=true
              ) + '\n' }}" # Add newline character here

        - name: Fetch the API-specific .env blob from Parameter Store
          set_fact:
            api_env_content: "{{ lookup(
              'aws_ssm',
              '/thankview-stage/thankview-api-env',
              region='us-east-1',
              decrypt=true
              ) + '\n' }}" # Add newline character here

        - name: Define list of .env paths
          set_fact:
            env_file_paths:
              - /var/www/thank-views/.env
              - /var/www/ThankView-API/.env
              - /var/www/ThankView-Envelope-Builder/.env
              - /var/www/thank-views-ca/.env

        - name: Stat parent directories of each .env file
          stat:
            path: "{{ item | dirname }}"
          loop: "{{ env_file_paths }}"
          register: env_stats
          loop_control:
            label: "{{ item }}"
          ignore_errors: yes

        - name: Overwrite existing .env files with the SSM blob
          copy:
            dest: "{{ item.item }}"
            content: "{% if item.item == '/var/www/ThankView-API/.env' %}{{ api_env_content }}{% else %}{{ env_content }}{% endif %}"
            owner: www-data
            group: www-data
            mode: '0644'
          loop: "{{ env_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item }}"
      become: true

    # Task to set APP_ROLE based on instance tags
    - name: Set APP_ROLE based on server type
      become: true
      block:
        - name: Check if APP_ROLE already exists in .env file
          shell: grep -c "^APP_ROLE=" /var/www/ThankView-Envelope-Builder/.env || true
          register: app_role_exists
          changed_when: false

        - name: Gather EC2 facts to get instance tags
          ec2_metadata_facts:
          register: ec2_facts

        - name: Get instance ID
          set_fact:
            instance_id: "{{ ec2_facts.ansible_facts.ansible_ec2_instance_id }}"
          when: ec2_facts is defined

        - name: Install Python pip
          apt:
            name: python3-pip
            state: present
            update_cache: yes

        - name: Install required Python modules for AWS operations
          pip:
            name:
              - boto3
              - botocore
            state: present
            executable: pip3
            extra_args: "--system"  # Install system-wide

        - name: Describe EC2 instance to get tags
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_facts.ansible_facts.ansible_ec2_placement_region }}"
          register: instance_info
          vars:
            ansible_python_interpreter: /usr/bin/python3  # Explicitly set Python interpreter
          when: instance_id is defined

        - name: Extract APP_ROLE from tags
          set_fact:
            app_role_value: "{{ item.value }}"
          loop: "{{ instance_info.instances[0].tags | dict2items }}"  # Fixed loop using dict2items
          when:
            - instance_info is defined
            - instance_info.instances is defined
            - instance_info.instances | length > 0
            - item.key == 'APP_ROLE'

        - name: Debug APP_ROLE value from tags
          debug:
            msg: "Found APP_ROLE tag with value: {{ app_role_value }}"
          when: app_role_value is defined and app_role_value != ''

        - name: Set APP_ROLE in .env file from instance tag
          lineinfile:
            path: /var/www/ThankView-Envelope-Builder/.env
            regexp: '^APP_ROLE='
            line: "APP_ROLE={{ app_role_value }}"
            state: present
          when:
            - app_role_exists.stdout == "0" or app_role_exists.stdout == "1"
            - app_role_value is defined
            - app_role_value != ''

    - name: Always restart PHP-FPM
      ansible.builtin.service:
        name: php8.1-fpm
        state: restarted
      become: true
