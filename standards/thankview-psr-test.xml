<?xml version="1.0"?>
<ruleset xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" name="PSR2" xsi:noNamespaceSchemaLocation="../../../phpcs.xsd">
    <description>The PSR-2 coding standard.</description>
    <arg name="tab-width" value="4"/>
    <rule ref="PSR2" />

    <rule ref="PSR2">
        <exclude name="PSR1.Methods.CamelCapsMethodName.NotCamelCaps" />
    </rule>
    <rule ref="PSR2">
        <exclude name="Generic.Files.LineLength" />
    </rule>
    <rule ref="PSR1">
	    <exclude name="PSR1.Classes.ClassDeclaration"/>
	</rule>
</ruleset>