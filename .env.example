APP_NAME=ThankView_Builder
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_PATH=/var/www/html
APP_TIMEZONE=UTC

BUILDER_PROD=ubuntu@**************:/var/www/ThankView-Envelope-Builder/public

LOG_CHANNEL=stack
LOG_SLACK_WEBHOOK_URL=

DB_CONNECTION=mysql
DB_HOST=mysql_eb
DB_PORT=3306
DB_DATABASE=builders
DB_USERNAME=root
DB_PASSWORD=root
DB_STRICT_MODE=false

TEST_DB_HOST=127.0.0.1
TEST_DB_PORT=3306
TEST_DB_DATABASE=builders_test
TEST_DB_USERNAME=root
TEST_DB_PASSWORD=root

CACHE_DRIVER=file
QUEUE_CONNECTION=sync
SENTRY_ENVIRONMENT=
MIX_BUILDER_APP_URL=https://dev-builder-thankview.com
MIX_THANKVIEW_APP_URL=https://dev-thankview.com

UPDATE_THANKVIEW=false
THANKVIEW_API_URL=https://dev-thankview.com/builder/
THANKVIEW_API_TOKEN=

AWS_KEY=
AWS_SECRET=

BUGSNAG_API_KEY=
ENVELOPE_PATH=testing-envelopes/
ENVELOPE_ASSETS_PATH=https://builder-assets.thankview.com/testing-envelopes/
FILESYSTEM_DRIVER=s3