#start with our base image (the foundation) - Jessie / php5.6 / apache
FROM php:8.1-apache

RUN docker-php-ext-install pdo_mysql
RUN a2enmod rewrite

# add keys
RUN apt-get update && apt-get install -y wget gnupg

#install all the system dependencies
RUN apt-get update && apt-get install -y --force-yes \
    libonig-dev \
    imagemagick \
    libmagickwand-dev \
    libzip-dev

#Installing PHP extensions
RUN docker-php-ext-configure pdo_mysql --with-pdo-mysql=mysqlnd \
    && docker-php-ext-install \
    pdo_mysql \
    opcache \
    intl \
    pcntl \
    mbstring \
    zip

#Install Xdebug
RUN pecl install xdebug \
    && docker-php-ext-enable xdebug
RUN echo "zend_extension = xdebug.so\n" \
    "xdebug.remote_enable = 1\n" \
    "xdebug.remote_autostart = 1\n" \
    "xdebug.remote_connect_back = 0\n" \
    "xdebug.remote_host = docker.for.mac.localhost\n" \
    "xdebug.remote_port = 9002\n" \
    > /usr/local/etc/php/conf.d/docker-php-ext-xdebug.ini

#download composer
COPY --from=composer:latest /usr/bin/composer /usr/bin/composer

#install imagemagick
RUN pecl install imagick && \
    echo "extension=imagick.so" > /usr/local/etc/php/conf.d/docker-php-ext-imagick.ini

#change uid and gid of apache to docker user uid/gid
RUN usermod -u 1000 www-data && groupmod -g 1000 www-data

#allow uploading of large files
RUN echo "file_uploads = On\n" \
    "memory_limit = 2G\n" \
    "upload_max_filesize = 2G\n" \
    "post_max_size = 2G\n" \
    "max_execution_time = 600\n" \
    > /usr/local/etc/php/conf.d/uploads.ini

#enable hosts
RUN a2ensite 000-default

#enable apache modules
RUN a2enmod rewrite headers ssl
RUN service apache2 restart

# Expose apache.
EXPOSE 80 443

#set directory
WORKDIR /var/www/html

ADD . /var/www
ADD ./public /var/www/html

#copy ssl certificates
COPY docker/ssl/server.crt /etc/apache2/ssl/server.crt
COPY docker/ssl/server.key /etc/apache2/ssl/server.key

# Run apache services
CMD apachectl -D FOREGROUND