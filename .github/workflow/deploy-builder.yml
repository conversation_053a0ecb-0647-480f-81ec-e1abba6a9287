name: Deploy ThankView App Staging

on:
  # push:
  #   branches:
  #     - staging
  #   paths:
  #     - 'thankview-deploy-stage/**'

  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (always staging)'
        required: true
        default: 'stage'
        type: string
      deploy_target:
        description: 'Server group to deploy to'
        required: true
        default: 'all'
        type: choice
        options:
          - all
          - web
          - media
          - secure
          - api
          - aws-workers
          - others
          - ca
          - builder
      deploy_branch:
        description: 'Git branch to deploy (Please enter the branch name to deploy from)'
        required: true
        default: 'staging'
        type: string

jobs:
  deploy:
    name: Deploy to staging
    runs-on: [self-hosted, Linux, X64, ec2, staging]

    env:
      AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
      AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
      AWS_REGION: us-east-1
      DEPLOY_ENV: 'stage'

    steps:
      - name: Checkout code (always from staging branch)
        uses: actions/checkout@v4
        with:
          ref: staging

      - name: List workflows and directory structure
        run: |
          echo "=== Listing all workflow files ==="
          find $GITHUB_WORKSPACE/.github/workflows -type f -ls
          echo ""
          echo "=== Finding thankview-deploy-stage directory ==="
          find $GITHUB_WORKSPACE -name "thankview-deploy-stage" -type d -ls

      - name: Verify System Python
        run: |
          python3 --version
          python3 -m pip install --upgrade pip

      - name: Install Ansible and dependencies
        run: |
          python3 -m pip install --upgrade pip
          python3 -m pip install --user ansible boto3 botocore
          echo 'export PATH=$HOME/.local/bin:$PATH' >> ~/.bashrc
          source ~/.bashrc
          export PATH=$HOME/.local/bin:$PATH
          which ansible
          which ansible-galaxy
          ansible-galaxy collection install amazon.aws community.general community.aws --force

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: us-east-1

      - name: Set up SSH keys
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.DEPLOY_SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -t rsa github.com >> ~/.ssh/known_hosts

      - name: Run inventory check
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-inventory --graph -i aws_ec2.aws_ec2.yml

      - name: Deploy - All Servers
        if: ${{ github.event.inputs.deploy_target == 'all' || github.event_name == 'push' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml main-deploy.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - Web Servers
        if: ${{ github.event.inputs.deploy_target == 'web' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml webserver-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - Media Servers
        if: ${{ github.event.inputs.deploy_target == 'media' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml media-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - Secure Servers
        if: ${{ github.event.inputs.deploy_target == 'secure' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml secure-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - API Servers
        if: ${{ github.event.inputs.deploy_target == 'api' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml api-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - AWS Workers
        if: ${{ github.event.inputs.deploy_target == 'aws-workers' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml worker-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - Other Servers
        if: ${{ github.event.inputs.deploy_target == 'others' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml other-servers-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - CA Servers
        if: ${{ github.event.inputs.deploy_target == 'ca' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml ca-deploy-main.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      - name: Deploy - Builder Servers
        if: ${{ github.event.inputs.deploy_target == 'builder' }}
        working-directory: ${{ github.workspace }}/thankview-deploy-stage
        run: ansible-playbook -i aws_ec2.aws_ec2.yml builder-deploy.yml -e "deploy_env=stage deploy_branch=${{ github.event.inputs.deploy_branch || 'staging' }}"

      # - name: Send notification
      #   if: always()
      #   working-directory: ${{ github.workspace }}/thankview-deploy-stage
      #   run: |
      #     DEPLOY_TARGET="${{ github.event.inputs.deploy_target || 'all' }}"
      #     DEPLOY_STATUS="${{ job.status }}"
      #     ansible-playbook -i aws_ec2.aws_ec2.yml notification.yml -e "deploy_env=$DEPLOY_ENV deploy_target=$DEPLOY_TARGET deploy_status=$DEPLOY_STATUS"
      #   continue-on-error: true