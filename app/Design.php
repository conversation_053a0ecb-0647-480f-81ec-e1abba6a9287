<?php namespace App;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Design extends Model
{

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'designs';

    /**
     * Uses soft deletes
     *
     */
    use SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'num_of_colors', 'category', 'order'];

    protected $dates = ['created_at', 'updated_at', 'deleted_at'];

    protected $casts = [
        'num_of_colors' => 'integer',
        'order' => 'integer'
    ];

    public function envelopes()
    {
        return $this->hasMany('App\Envelope');
    }
}
