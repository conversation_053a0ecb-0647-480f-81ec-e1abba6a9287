<?php

namespace App\Http\Middleware;

use Closure;
use App\Services\ThankViewService;

class EnvelopeCountMiddleware
{
    private $thankViewService;

    public function __construct(ThankViewService $thankViewService)
    {
        $this->thankViewService = $thankViewService;
    }
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        $design = $request->has('design') ? $request->input('design') : [];
        $metadata = isset($design['metadata']) ? json_decode($design['metadata']) : null;
        if ($metadata && isset($metadata->uuid)) {
            $res = $this->thankViewService->availableCount($metadata->uuid);
            if ($res['success'] && $res['data']['envelopes_available'] < 1) {
                return response()->json(['success' => false, 'error' => 'No envelopes available', 'data' => ['envelopes_available' => 0]]);
            }
        }
        return $next($request);
    }
}
