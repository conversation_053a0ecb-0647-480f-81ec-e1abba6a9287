<?php namespace App\Http\Middleware;

use Closure;

class ApiGateway
{

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle($request, Closure $next)
    {
        if ($request->header('Api-Gateway-Token') != env('THANKVIEW_API_TOKEN')) {
            return response('Unauthorized.', 401);
        }
        return $next($request);
    }
}
