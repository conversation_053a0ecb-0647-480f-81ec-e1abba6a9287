<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Envelope;
use App\Services\AWSService;
use App\Services\UploadService;
use App\Services\ThankViewService;
use App\Services\UtilitiesService;
use Log;

class BackgroundImageController extends Controller
{
    public function index($uuid, $landingPageIdString, ThankViewService $tvService)
    {
        return $tvService->getBackgroundImages($uuid, $landingPageIdString);
    }

    public function store(Request $request, ThankViewService $tvService)
    {
        $this->validate($request, [
            'metadata.uuid' => 'required',
            'name' => 'required',
            'bkgdImage' => 'required',
        ]);

        $data = [
            'uuid' => $request->input('metadata.uuid'),
            'name' => $request->input('name'),
        ];

        $files = [
            'bkgdImage' => UtilitiesService::base64ImageToFile($request->input('bkgdImage')),
        ];

        return $tvService->createBackgroundImage($data, $files);
    }

    public function destroy($uuid, $id, ThankViewService $tvService)
    {
        return $tvService->deleteBackgroundImage($uuid, $id);
    }

    public function rename(Request $request, ThankViewService $tvService)
    {
        $this->validate($request, [
            'metadata.uuid' => 'required',
            'id' => 'required',
            'name' => 'required',
        ]);

        $data = [
            'uuid' => $request->input('metadata.uuid'),
            'id' => $request->input('id'),
            'name' => $request->input('name'),
        ];

        return $tvService->renameBackgroundImage($data);
    }
}
