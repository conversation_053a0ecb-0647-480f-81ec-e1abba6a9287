<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\Envelope;
use App\Services\AWSService;
use App\Services\ImageService;
use App\Services\UploadService;
use App\Services\ThankViewService;
use App\Services\UtilitiesService;
use Illuminate\Support\Facades\Log;

class EnvelopeController extends Controller
{
    public function count($uuid, ThankViewService $tvService)
    {
        return $tvService->availableCount($uuid);
    }

    public function getEnvelope($uuid, $envelopeSlug, AWSService $awsService)
    {
        // TODO: Add some uuid checking later
        $envelope = Envelope::where('slug', $envelopeSlug)->firstOrFail();

        // Inject images
        if ($envelope->has_front_logo) {
            $front_logo_data = $this->getEnvelopeImage($envelopeSlug, 'front-logo.png', $awsService);
            $envelope->front_logo = 'data:image/jpeg;base64,' . base64_encode($front_logo_data);
        }
        if ($envelope->has_back_logo) {
            $back_logo_data = $this->getEnvelopeImage($envelopeSlug, 'back-logo.png', $awsService);
            $envelope->back_logo = 'data:image/jpeg;base64,' . base64_encode($back_logo_data);
        }
        if ($envelope->has_stamp_image) {
            $stamp_image_data = $this->getEnvelopeImage($envelopeSlug, 'stamp-image.png', $awsService);
            $envelope->stamp_image = 'data:image/jpeg;base64,' . base64_encode($stamp_image_data);
        }

        return response()->json(['success' => true, 'data' => ['envelope' => $envelope]]);
    }

    public function store(Request $request, AWSService $awsService)
    {
        $params = $request->input('design');
        $slug = uniqid();
        $params['slug'] = $slug;
        $params['path'] = config('paths.envelopes.url') . $slug;

        try {
            mkdir(temp_path() . 'envelopes/' . $slug);
            $params = $this->handleEnvelopePieces($request, $slug, $params, $awsService);
        } catch (Exception $e) {
            return response()->json(['success' => false, 'error' => 'Could not store envelope: '. $e->getMessage()]);
        }

        $envelope = Envelope::create($params);

        return response()->json(['success' => true, 'data' => ['envelope' => $envelope]]);
    }

    public function update($envelopeId, Request $request, UploadService $uploadService, AWSService $awsService)
    {
        $params = $request->input('design');
        $envelope = Envelope::findOrFail($envelopeId);
        $slug = $envelope->slug;
        $newSlug = uniqid();

        try {
            UtilitiesService::deleteFiles(temp_path() . 'envelopes/' . $slug);
            mkdir(temp_path() . 'envelopes/' . $newSlug);

            $params = $this->handleEnvelopePieces($request, $newSlug, $params, $awsService, $slug);

            $envelope->slug = $newSlug;
            $envelope->save();
        } catch (Exception $e) {
            return response()->json(['success' => false, 'error' => 'Could not update envelope: ' . $e->getMessage()]);
        }
        $params['path'] = config('paths.envelopes.url') . $envelope->slug;
        $envelope->update($params);

        return response()->json(['success' => true, 'data' => ['envelope' => $envelope]]);
    }

    public function thankviewDelete(Request $request)
    {
        $ids = $request->input('ids');
        $result = Envelope::destroy($ids);
        return response()->json(['success' => true]);
    }

    private function storePieces($slug, $uploadService, $envelopePiecesPath)
    {
        $prodPath = config('paths.envelopes.storage') .  $slug . '/';
        $params = ['CacheControl' => config('filesystems.cache_control')];
        $uploadService->putDir($prodPath, $envelopePiecesPath, $params);
    }

    /**
     * Resizes images to be max 500px on any side while keeping aspect ratio in order to reduce size when uploaded to AWS
     *
     * @param String $filePath
    */
    private function resizeAsset($filePath)
    {
        $imageService = app()->makeWith(ImageService::class, [$filePath]);

        $width = $imageService->getWidth();
        $height = $imageService->getHeight();
        if (($width / 500) > 1 || ($height / 500) > 1) {
            $divider = ceil(max(($width / 500), ($height / 500)));

            $imageService->resize($width / $divider, $height / $divider);
            $imageService->writeImage($filePath);
        }
    }

    /**
     * @param Request $request
     * @param string $slug
     * @param $params
     * @param AWSService $awsService
     * @param $oldSlug
     * @return mixed
     */
    private function handleEnvelopePieces(Request $request, string $slug, $params, AWSService $awsService, $oldSlug = null)
    {
        // The temporary local folder where envelope pieces will be stored in preparation for uploading
        $envelopePiecesPath = 'envelopes/'. $slug;

        // Store the envelope pieces which were generated on the frontend
        UtilitiesService::storeBase64AsFile($request->input('back'), $envelopePiecesPath . '/back.jpg');
        UtilitiesService::storeBase64AsFile($request->input('flap-top-close'), $envelopePiecesPath . '/flap-top-close.png');
        UtilitiesService::storeBase64AsFile($request->input('flap-top-open'), $envelopePiecesPath . '/flap-top-open.png');
        UtilitiesService::storeBase64AsFile($request->input('flaps'), $envelopePiecesPath . '/flaps.png');
        UtilitiesService::storeBase64AsFile($request->input('front-small'), $envelopePiecesPath . '/front-small.jpg');
        UtilitiesService::storeBase64AsFile($request->input('front'), $envelopePiecesPath . '/front.jpg');
        UtilitiesService::storeBase64AsFile($request->input('icon'), $envelopePiecesPath . '/icon.jpg');
        UtilitiesService::storeBase64AsFile($request->input('share'), $envelopePiecesPath . '/share.png');

        // Only show the front/back logo if the indicator and the image are set
        $params['has_front_logo'] = $params['show_front_logo'] && !empty($params['front_logo']);
        $params['has_back_logo'] = $params['show_back_logo'] && !empty($params['back_logo']);
        $params['has_stamp_image'] = !empty($params['stamp_image']);

        // Store the Front Logo, Back Logo, and Stamp Image for when the user edits this envelope
        // If updating an existing envelope and the image hasn't changed, the images are copied from the old envelope instead
        if ($params['has_front_logo']) {
            $this->storeUserImage($params['front_logo'], $oldSlug, $envelopePiecesPath, 'front-logo.png', $awsService);
            $this->resizeAsset(temp_path() . $envelopePiecesPath .'/front-logo.png');
        }

        if ($params['has_back_logo']) {
            $this->storeUserImage($params['back_logo'], $oldSlug, $envelopePiecesPath, 'back-logo.png', $awsService);
        }

        if ($params['has_stamp_image']) {
            $this->storeUserImage($params['stamp_image'], $oldSlug, $envelopePiecesPath, 'stamp-image.png', $awsService);
            $this->resizeAsset(temp_path() . $envelopePiecesPath .'/stamp-image.png');
        }

        // Envelopes are only editable if they were created after we started tracking the form inputs
        $params['is_editable'] = true;

        $this->storePieces($slug, $awsService, temp_path() . $envelopePiecesPath);

        return $params;
    }

    /**
     * When updating an existing envelope with images, the frontend will only have a URL available instead of a base 64 string
     * If the image uploaded is a URL, it will be copied from the already stored image in AWS
     * Otherwise (if the image is a base 64 string) it will be turned into a file and stored in AWS
     * Note: This only applies to images chosen by the user
     * @param $image - URL to a file in AWS or a Base 64 string
     */
    private function storeUserImage($image, $oldSlug, $envelopePiecesPath, $fileName, $awsService)
    {
        $isUrl = filter_var($image, FILTER_VALIDATE_URL);
        if ($isUrl && $oldSlug) {
            $this->copyEnvelopeImage($oldSlug, $envelopePiecesPath, $fileName, $awsService);
        } else {
            UtilitiesService::storeBase64AsFile($image, $envelopePiecesPath . '/' . $fileName);
        }
    }

    /**
     * Copies an existing envelope image, used when updating an envelope
     * @param $oldSlug
     * @param $envelopePiecesPath
     * @param $fileName
     * @param $awsService
     */
    private function copyEnvelopeImage($oldSlug, $envelopePiecesPath, $fileName, $awsService)
    {
        $existingImage = $this->getEnvelopeImage($oldSlug, $fileName, $awsService);

        $newFilePath = temp_path() . $envelopePiecesPath . '/' . $fileName;
        file_put_contents($newFilePath, $existingImage);
    }

    private function getEnvelopeImage($slug, $fileName, $awsService)
    {
        $filePath = config('paths.envelopes.storage') .  $slug . '/' . $fileName;
        return $awsService->get($filePath);
    }
}
