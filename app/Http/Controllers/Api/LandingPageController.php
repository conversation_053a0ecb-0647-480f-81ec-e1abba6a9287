<?php

namespace App\Http\Controllers\Api;

use App\Services\UploadService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;

use App\LandingPage;
use App\Services\ThankViewService;
use App\Services\UtilitiesService;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class LandingPageController extends Controller
{
    /**
     * Returns landing_pages_used and landing_pages_credited
     * @param $uuid
     * @param Request $request
     * @param ThankViewService $tvService
     * @return array|mixed
     */
    public function stats($uuid, ThankViewService $tvService)
    {
        return $tvService->getLandingPageStats($uuid);
    }

    public function index($uuid, ThankViewService $tvService)
    {
        return $tvService->getLandingPages($uuid);
    }

    public function getLandingPage($uuid, $id, ThankViewService $tvService)
    {
        return $tvService->getLandingPage($uuid, $id);
    }

    public function store(Request $request, ThankViewService $tvService, UploadService $uploadService)
    {
        $metadata = $request->input('metadata');
        $uuid = $metadata['uuid'] ?? null;
        $user_id = $metadata['user_id'] ?? null;
        $name = $request->input('name');

        $data = [
            'uuid' => $uuid,
            'user_id' => $user_id,
            'name' => $name,
            'header_bkgd_color' => $request->input('header_bkgd_color'),
            'bkgd_image_id' => $request->input('bkgd_image_id'),
            'button_text_color' => $request->input('button_text_color'),
            'button_color' => $request->input('button_color'),
            'button_reply_color' => $request->input('button_reply_color'),
            'button_save_color' => $request->input('button_save_color'),
            'button_share_color' => $request->input('button_share_color'),
            'button_below_text_color' => $request->input('button_below_text_color'),
            'hide_message_gradient' => $request->boolean('hide_message_gradient') ? 'on' : 'off',
            'message_bkgd_texture' => $request->boolean('cta_text_bkgd') ? 'on' : 'off',
            'bkgd_anchor_vertical' => $request->input('bkgd_anchor_vertical'),
            'bkgd_anchor_horizontal' => $request->input('bkgd_anchor_horizontal'),
        ];

        if ($request->input('header_logo_file')) {
            $files = [
                // New uploaded header logo (if it was changed)
                'headerImage' => UtilitiesService::base64ImageToFile($request->input('header_logo_file')),
            ];
        } else {
            $files = [];
        }

        return $tvService->createLandingPage($data, $files);
    }

    public function update($id, Request $request, ThankViewService $tvService)
    {
        $metadata = $request->input('metadata');
        $uuid = $metadata['uuid'] ?? null;
        $name = $request->input('name');

        $data = [
            'uuid' => $uuid,
            'name' => $name,
            'id' => $id,
            'header_bkgd_color' => $request->input('header_bkgd_color'),
            'bkgd_image_id' => $request->input('bkgd_image_id'),
            'button_text_color' => $request->input('button_text_color'),
            'button_color' => $request->input('button_color'),
            'button_reply_color' => $request->input('button_reply_color'),
            'button_save_color' => $request->input('button_save_color'),
            'button_share_color' => $request->input('button_share_color'),
            'button_below_text_color' => $request->input('button_below_text_color'),
            'hide_message_gradient' => $request->boolean('hide_message_gradient') ? 'on' : 'off',
            'message_bkgd_texture' => $request->boolean('cta_text_bkgd') ? 'on' : 'off',
            'is_active' => 'on',
            'removeHeaderImage' => $request->input('remove_header_image'),
            'bkgd_anchor_vertical' => $request->input('bkgd_anchor_vertical'),
            'bkgd_anchor_horizontal' => $request->input('bkgd_anchor_horizontal'),
        ];

        if ($request->input('header_logo_file')) {
            $files = [
                // New uploaded header logo (if it was changed)
                'headerImage' => UtilitiesService::base64ImageToFile($request->input('header_logo_file')),
            ];
        } else {
            $files = [];
        }

        return $tvService->updateLandingPage($data, $files);
    }
}
