<?php


namespace App\Http\Controllers\Api;

use App\Services\ThankViewService;
use Illuminate\Http\Request;

class LogEventsController
{
    public function addEvent(Request $request, ThankViewService $thankViewService)
    {
        $name = $request->input('eventName');
        $type = $request->input('eventType');

        $metadata = $request->input('metadata');
        $user_id = $metadata['user_id'] ?? null;

        return $thankViewService->addLogEvent($name, $type, $user_id);
    }
}
