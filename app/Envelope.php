<?php namespace App;

use Cache;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

use App\Traits\NotifyThankView;

class Envelope extends Model
{

    /**
     * The database table used by the model.
     *
     * @var string
     */
    protected $table = 'envelopes';

    /**
     * Uses soft deletes
     *
     */
    use SoftDeletes;

    /**
     * Updates ThankView database if env is set to 'enabled'
     */
    use NotifyThankView;
    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = ['name', 'slug', 'primary_color', 'secondary_color', 'tertiary_color', 'envelope_color', 'envelope_text_color', 'liner_color', 'postmark_copy', 'color_1', 'color_2', 'color_3', 'metadata', 'design_id', 'path', 'has_front_logo', 'has_back_logo', 'has_stamp_image', 'stamp_zoom_level', 'postmark_color', 'is_editable'];

    protected $dates = ['created_at', 'updated_at', 'deleted_at'];

    protected $thankViewData = ['id', 'slug', 'name', 'envelope_color', 'envelope_text_color', 'path', 'is_editable'];

    protected $casts = [
        'has_front_logo' => 'boolean',
        'has_back_logo' => 'boolean',
        'has_stamp_image' => 'boolean'
    ];

    public function design()
    {
        return $this->belongsTo('App\Design', 'design_id');
    }
}
