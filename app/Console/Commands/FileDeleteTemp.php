<?php
namespace App\Console\Commands;

use Exception;
use Illuminate\Console\Command;
use App\Services\UtilitiesService;

/**
 * Class tempDirCleanUpCommand
 *
 * @category Console_Command
 * @package  App\Console\Commands
 */
class FileDeleteTemp extends Command
{
    /**
     * The console command name.
     *
     * @var string
     */
    protected $signature = "file:deleteTemp";

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = "Delete all temp files/folders after 4 hours";

    /**
     * Execute the console command.
     *
     * @return mixed
     */
    public function handle()
    {
        $this->deleteTempEnvelopes();
    }

    private function deleteTempEnvelopes()
    {
        $fourHours = 4 * 60 * 60;
        // Clear /storage/tmp/envelopes
        $envelopes = glob(temp_path() . 'envelopes/*');
        $now = time();

        foreach ($envelopes as $envelope) {
            $files = glob($envelope . '/*');
            if (!count($files)) {
                UtilitiesService::deleteFiles($envelope);
            } else {
                $firstFile = array_values($files)[0];
                if (filemtime($firstFile) && ($now - filemtime($firstFile) >= $fourHours)) {
                    UtilitiesService::deleteFiles($envelope);
                }
            }
        }

        // Clear /storage/tmp/images
        $files = glob(temp_path() . 'images/*');
        $now = time();

        foreach ($files as $file) {
            if (is_file($file) && filemtime($file)) {
                if ($now - filemtime($file) >= $fourHours) {
                    unlink($file);
                }
            }
        }
    }
}
