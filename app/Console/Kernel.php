<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use <PERSON><PERSON>\Lumen\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        \Laravel\Tinker\Console\TinkerCommand::class,
        Commands\FileDeleteTemp::class
    ];

    /**
     * Define the application's command schedule.
     *
     * @param  \Illuminate\Console\Scheduling\Schedule  $schedule
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {
        // schedule removing temp files
        $schedule->command('file:deleteTemp')->hourly();
    }
}
