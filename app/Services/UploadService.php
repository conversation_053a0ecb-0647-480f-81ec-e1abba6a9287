<?php

namespace App\Services;

use App\Services\AWSService;
use App\Services\ImageService;
use Config;
use Illuminate\Support\Facades\Log;

/**
 * Wrapper to write to filesystem and process images
 */
class UploadService
{
    protected $awsService;
    protected $root;
    protected $fileDir;
    protected $bkgdDir;
    protected $tmpDir;

    public function __construct()
    {
        $this->awsService = new AWSService();
        $this->root = '';
        $this->fileDir = public_path() . '/files/';
        $this->bkgdDir = $this->root . 'img/bkgds/';
        $this->tmpDir = storage_path() . '/tmp/';
    }

    /**
     * Processes backround image to set sizes and converts pngs to jpgs
     *
     * @param string $content - filepath to background image
     *
     * @return string - Returns temporary file path where processed background image is stored
     *
     */
    private function processBkgd($content)
    {
        $ext = pathinfo($content->getClientOriginalName(), PATHINFO_EXTENSION);
        $tmp_path = $this->tmpDir . 'tmp' . '.' . $ext;
        $imageService = app()->makeWith(ImageService::class, [$content->getRealPath()]);
        $imageService->setMaxSize(2000, 2000 * 100);
        $imageService->convertPngToJpg();
        $imageService->compressJpg();
        $imageService->writeImage($tmp_path);
        return $tmp_path;
    }

    /**
     * Stores processed background image
     *
     * @param string $content - filepath to background image
     *
     * @param string $filename - filename for the processed background image
     *
     * @return string - Returns public path to processed background image
     */
    public function storeBkgds($content, $filename)
    {
        $root = $this->bkgdDir;
        $ext = pathinfo($content->getClientOriginalName(), PATHINFO_EXTENSION);
        $options = ['CacheControl' => Config::get('filesystems.cache_control')];

        $file_path = $root . $filename . '.' . $ext;
        $tmp_path = $this->processBkgd($content);
        $this->awsService->put($file_path, file_get_contents($tmp_path), $options);
        unlink($tmp_path);

        return (env('APP_CDN_HOST') ?: '/') . $file_path;
    }

    /**
     * Stores json file
     *
     * @param string $content - filepath to json file
     *
     * @param string $filename - filename to name the saved json file
     *
     * @param boolean $useTmp - (Optional) stores json file to temporary path
     *
     * @return string - Returns public path of json file
     */
    public function storeJsonToFile($content, $filename, $useTmp = false)
    {
        $root = (!$useTmp) ? $this->fileDir : $this->tmpDir;
        $path = $root . $filename . '.json';
        $file = fopen($path, 'w');
        file_put_contents($path, "");
        file_put_contents($path, $content);
    }

    /**
     * Reads json file from path
     *
     * @param string $filename - filename of json file
     *
     * @param boolean $useTmp - (Optional) Look inside temp directory
     *
     * @return Json - Returns json file data as decoded json
     */
    public function getJsonFromFile($filename, $useTmp = false)
    {
        $root = (!$useTmp) ? $this->fileDir : $this->tmpDir;
        $path = $root . $filename . '.json';

        if (!file_exists($path)) {
            $file = fopen($path, 'w');
        }

        $content = file_get_contents($path);
        $data = json_decode($content);
        return $data;
    }


    /**
     * Updates the name of the file given a path and new filename
     *
     * @param string $oldFilename - filepath of the original file
     *
     * @param string $newFilename - filepath of the renamed file
     *
     * @return string - Returns filepath of renamed file
     */
    public function updatePath($oldFilename, $newFilename)
    {
        $oldPath = $this->root . $oldFilename;
        $newPath = $this->root . $newFilename;

        $res = $this->awsService->rename($oldPath, $newPath);
        return $res;
    }

    /**
     * Gets the temp directory
     *
     * @return string - Returns the filepath to the temp directory in the project
     */
    public function getTempPath()
    {
        return $this->tmpDir;
    }

    /**
     * Stores content from filepath into desired directory with desired filename
     *
     * @param string $content - filepath to read file from
     *
     * @param string $filename - The name of the file to be stored
     *
     * @param string $type - (Optional) The path type as speicified by paths config file - the full path a file belongs to
     *
     * @return null
     */
    public function store($content, $filename, $type = 'local')
    {
        $path = Config::get('paths.' . $type) . $filename;

        //resolves memory issue with laravel detecting mime type in production
        $detector = new \League\MimeTypeDetection\FinfoMimeTypeDetector();
        $mimeType = $detector->detectMimeTypeFromPath($filename);

        $options = [
            'CacheControl' => Config::get('filesystems.cache_control'),
            'mimetype' => $mimeType,
        ];

        $awsService = new AWSService();
        $awsService->put($path, file_get_contents($content), $options);

        return $path;
    }

    /**
     * Generates a media path given a filename and path type
     *
     * @param string $filename - The name of the file
     *
     * @param string $type - (Optional)  The path type as speicified by paths config fil - the full path a file belongs to
     *
     */
    public static function getMediaPath($filename, $type = 'local')
    {
        return media_path() . Config::get('paths.' . $type) . $filename;
    }
}
