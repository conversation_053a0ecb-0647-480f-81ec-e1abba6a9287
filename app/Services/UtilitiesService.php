<?php namespace App\Services;

use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\File\UploadedFile;

class UtilitiesService
{
    //weird merge conflicts
    //used to delete a directory and all of its subsequent files/directories recursively
    public static function deleteFiles($target = null)
    {
        if (is_dir($target)) {
            $files = glob($target . '*', GLOB_MARK); //GLOB_MARK adds a slash to directories returned

            foreach ($files as $file) {
                UtilitiesService::deleteFiles($file);
            }
            if (file_exists($target)) {
                rmdir($target);
            }
        } elseif (is_file($target)) {
            unlink($target);
        }
    }

    /**
     * Stores a Base 64 string as a file in temporary local storage (/tmp/ folder)
     * @param $base64DataString
     * @param $filePath
     * @param bool $appendExtension If true, the file extension will be appended
     * @return string
     */
    public static function storeBase64AsFile($base64DataString, $filePath, $appendExtension = false)
    {
        // split the string on the right into two, then assign $type to the first part and $data to the second part
        list($type, $data) = explode(';', $base64DataString);

        if ($appendExtension) {
            // $type looks like "data:@file/png", extract the section after the slash
            $type = substr($type, strpos($type, '/') + 1);
            // Add the extension to the file path
            $filePath .= '.' . $type;
        }

        // split the $data that was just assigned, re assign it to part of the string after ,
        list(, $data) = explode(',', $data);
        $data = base64_decode($data);

        file_put_contents(temp_path() . $filePath, $data);

        return $filePath;
    }

    /**
     * Converts an image from Base 64 to an UploadedFile object and returns the object
     * @uses UtilitiesService::storeBase64AsFile()
     * @param $headerLogoFile
     * @param $uploadService
     * @return UploadedFile
     */
    public static function base64ImageToFile($headerLogoFile)
    {
        $fileName = UtilitiesService::storeBase64AsFile($headerLogoFile, 'images/' . uniqid(), true);
        $fileName = str_replace('images/', '', $fileName);

        return new UploadedFile(temp_path() . 'images/' . $fileName, $fileName);
    }
}
