<?php

namespace App\Services;

use Config;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

use Bugsnag\BugsnagLaravel\Facades\Bugsnag;

class ThankViewService
{
    protected $url;

    public function __construct($url, $header, $is_prod)
    {
        $this->url = $url;
        $this->http = Http::withHeaders($header)->withOptions(['verify' => $is_prod]);
    }

    public function availableCount($uuid)
    {
        $response = $this->http->get($this->url .  'envelopes/availableCount?uuid=' . $uuid);
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    public function store($data, $tries = 0)
    {
        try {
            $response = $this->http->post($this->url . $data['table'], $data);
        } catch (\Exception $e) {
            if ($tries < 3) {
                sleep(1);
                $this->store($data, $tries + 1);
            } else {
                Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
                return ['success' => false, 'error' => 'Error communicating with ThankView API'];
            }
        }

        return $this->parseResponse($response, 'storing');
    }

    public function update($data, $tries = 0)
    {
        try {
            $response = $this->http->put($this->url . $data['table'], $data);
        } catch (\Exception $e) {
            if ($tries < 3) {
                sleep(1);
                $this->update($data, $tries + 1);
            } else {
                Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
                return ['success' => false, 'error' => 'Error communicating with ThankView API'];
            }
        }

        return $this->parseResponse($response, 'updating');
    }

    /**
     * Get background images from the ThankView App
     * @param $uuid string Unique identifier for a business
     * @return array
     */
    public function getBackgroundImages($uuid, $landingPageIdString)
    {
        $response = $this->http->get($this->url .  'backgroundImages?uuid=' . $uuid . '&landingPageIdString=' . $landingPageIdString);
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Store a background image on the ThankView App table project_background_images
     * @return array
     */
    public function createBackgroundImage($data, $files)
    {
        $this->createMultipartRequest($data, $files);
        $response = $this->http->post($this->url . 'backgroundImage');

        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Delete a background image on the ThankView App table project_background_images
     * @return array
     */
    public function deleteBackgroundImage($uuid, $id)
    {
        $response = $this->http->delete($this->url .  'backgroundImage/' . $uuid . '/' . $id);
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Rename a background image on the ThankView App table project_background_images
     * @return array
     */
    public function renameBackgroundImage($data)
    {
        $response = $this->http->post($this->url .  'backgroundImage/rename', $data);

        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Get one landing page ($id) from the business with the given $uuid
     * @return array
     */
    public function getLandingPage($uuid, $id)
    {
        $response = $this->http->get($this->url .  'landingPage?uuid=' . $uuid . "&id=" . $id);
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Get all Landing Pages for the Business with the given $uuid
     * @return array
     */
    public function getLandingPages($uuid)
    {
        $response = $this->http->get($this->url .  'landingPages?uuid=' . $uuid);
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    public function getLandingPageStats($uuid)
    {
        $response = $this->http->get($this->url .  'landingPageStats?uuid=' . $uuid);
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Store a landing page on the ThankView App table landing_pages
     * @return array
     */
    public function createLandingPage($data, $files)
    {
        $this->createMultipartRequest($data, $files);
        $response = $this->http->post($this->url . 'landingPage');

        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Store a landing page on the ThankView App table landing_pages
     * @return array
     */
    public function updateLandingPage($data, $files)
    {
        $this->createMultipartRequest($data, $files);
        $response = $this->http->post($this->url . 'updateLandingPage');

        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }

    /**
     * Sets up a Multipart Request on the $this->http variable using an array of data and an array of files
     * @param $data
     * @param $files
     */
    private function createMultipartRequest($data, $files)
    {
        // For each data item, attach a key value pair if the value exists
        foreach ($data as $key => $value) {
            if (!$value) {
                continue;
            }
            $this->http->attach($key, $value);
        }

        // For each file, attach a key value pair if the file exists
        foreach ($files as $key => $value) {
            if (!$value) {
                continue;
            }
            $this->http->attach($key, file_get_contents($value->getRealPath()), $value->getClientOriginalName());
        }
    }

    public function purgeEnvelopes()
    {
        //TODO: add function to query for existence of envelopes with uuid in metadata in thankview-app
        // if deleted in thankview-app, delete from s3 and builder db
    }

    private function parseResponse($response, $type)
    {
        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        $responseData = $response->json();
        if ($responseData && $responseData['success']) {
            return ['success' => true];
        }
        return ['success' => false, 'error' => 'Error ' . $type . ' envelope'];
    }

    public function addLogEvent($name, $type, $userId)
    {
        $response = $this->http->post($this->url .  'addLogEvent', ['eventName' => $name, 'eventType' => $type, 'userId' => $userId]);

        if (!$response->ok()) {
            Bugsnag::notifyException(new \RuntimeException("Error communicating with the ThankView API"));
            return ['success' => false, 'error' => 'Error communicating with ThankView API'];
        }
        return $response->json();
    }
}
