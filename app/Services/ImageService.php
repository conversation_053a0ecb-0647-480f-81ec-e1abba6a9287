<?php namespace App\Services;

use Config;
use Imagick;
use ImagickPixel;

class ImageService
{

    /**
     * Create a new controller instance.
     *
     * @return Null
     */
    public function __construct($imagePath)
    {
        $this->image = new Imagick($imagePath);
    }

    /**
     * Sets the orientation of the image so it's upright.
     *
     * @return Null
     */
    public function fixOrientation()
    {
        $orientation = $this->image->getImageOrientation();

        switch ($orientation) {
            case Imagick::ORIENTATION_BOTTOMRIGHT:
                $this->image->rotateimage('#000', 180);
                break;

            case Imagick::ORIENTATION_RIGHTTOP:
                $this->image->rotateimage('#000', 90);
                break;

            case Imagick::ORIENTATION_LEFTBOTTOM:
                $this->image->rotateimage('#000', -90);
                break;
        }

        $this->image->setImageOrientation(Imagick::ORIENTATION_TOPLEFT);
    }

    /**
     * Set the maximum width and height of the image.
     * If the image is smaller than the dimensions, keep the image
     * at the same size.
     *
     * @param Int $maxWidth - the maximum width in pixels
     * @param Int $maxHeight - the maximum height in pixels
     *
     * @return Null
     */
    public function setMaxSize($maxWidth, $maxHeight)
    {
        if ($this->image->getImageWidth() > $maxWidth || $this->image->getImageHeight() > $maxHeight) {
            $this->resize($maxWidth, $maxHeight);
        }
    }

    /**
     * Returs the height of the image
     *
     * @return Int - height of the image in pixels
     */
    public function getHeight()
    {
        return $this->image->getImageHeight();
    }

    /**
     * Returns the width of the image
     *
     * After images are create, we store them using the Storage facade.
     *
     * @return Int - width of the image in pixels
     */
    public function getWidth()
    {
        return $this->image->getImageWidth();
    }

    /**
     * Resizes the image to a specific width and height without changing the aspect ratio.
     * Also supports gifs
     *
     * @param Int $width - the width in pixels to resize to
     * @param Int $height - the height in pixels to resize to
     *
     * @return Null
     */
    public function resize($width, $height)
    {
        $format = $this->image->getImageFormat();
        if ($format == 'GIF') {
            $this->image = $this->image->coalesceImages();
            do {
                $this->image->resizeImage($width, $height, Imagick::FILTER_LANCZOS, 1, true);
            } while ($this->image->nextImage());
        } else {
            $this->image->resizeImage($width, $height, Imagick::FILTER_LANCZOS, 1, true);
        }
    }

    /**
     * Outputs the current image to a specific filepath
     *
     * @param String $filePath - the path to write the file to
     *
     * @return Null
     */
    public function writeImage($filePath)
    {
        $format = $this->image->getImageFormat();
        if ($format == 'GIF') {
            $this->image = $this->image->coalesceImages();
            $this->image->writeImages($filePath, true);
        } else {
            $this->image->writeImage($filePath);
        }
    }

    /**
     * Converts a png image to a jpg.
     * Adds a white background to fill in transparent pixels
     *
     * @return Null
     */
    public function convertPngToJpg()
    {
        $this->image->setImageBackgroundColor('white');
        $this->image = $this->image->mergeImageLayers(Imagick::LAYERMETHOD_FLATTEN);

        $this->image->setImageFormat('jpg');
    }

    /**
     * Compresses jpg on a scale from 1 to 100
     *
     * @param Int $quality - level of compression from 1 to 100
     *
     * @return Null
     */
    public function compressJpg($quality = 85)
    {
        $this->image->setImageCompression(imagick::COMPRESSION_JPEG);
        $this->image->setImageCompressionQuality($quality);
        $this->image->stripImage();
    }

    /**
     * Trims the edges of the image
     *
     * @return Null
     */
    public function trimImage()
    {
        $this->image->trimImage(0);
    }

    /**
     * Changes a jpg's color space from CMYK to RGB
     * so it's compatible with our app.
     *
     * @return Null
     */
    public function fixColorspace()
    {
        if ($this->image->getImageColorspace() == Imagick::COLORSPACE_CMYK) {
            $path = Config::get('ffmpeg.resources') . 'assets/color_profiles';

            $profiles = $this->image->getImageProfiles('*', false);
            // we're only interested if ICC profile(s) exist
            $has_icc_profile = (array_search('icc', $profiles) !== false);
            // if it doesnt have a CMYK ICC profile, we add one
            if ($has_icc_profile === false) {
                $icc_cmyk = file_get_contents($path . '/USWebUncoated.icc');
                $this->image->profileImage('icc', $icc_cmyk);
                unset($icc_cmyk);
            }
            // then we add an RGB profile
            $icc_rgb = file_get_contents($path . '/sRGB_v4_ICC_preference.icc');
            $this->image->profileImage('icc', $icc_rgb);
            unset($icc_rgb);

            $this->image->stripImage();
        }
    }

    /**
     * Overlays an image on top of our existing image.
     *
     * @param String $overlayImagePath - the path to the image to overlay
     * @param Int $width - the width of the overlay image
     * @param Int $height - the height of the overlay image
     *
     * @return Null
     */
    public function overlayImages($overlayImagePath, $width, $height)
    {
        //overlayImagePath goes on top.
        $format = $this->image->getImageFormat();
        $overlay = new Imagick($overlayImagePath);
        $overlay->resizeImage($width, $height, Imagick::FILTER_LANCZOS, 1, true);
        if ($format == 'GIF') {
            // Should this be $this->image??
            $this->video->overlayImagick($overlay, $width, $height);
        } else {
            $this->image->compositeImage($overlay, Imagick::COMPOSITE_DEFAULT, 0, 0);
        }
    }

    /**
     * Takes an Imagick Image/Gif and overlays it on top of the current image/gif
     *
     * @param Imagick $imagickImage - the path to the image to overlay
     * @param Int $xVal - position of overlay in x direction (pixels)
     * @param Int $yVal - position of overlay in y direction (pixels)
     *
     * @return Null
     */
    public function overlayImagick($imagickImage, $xVal, $yVal)
    {
        $format = $this->image->getImageFormat();
        $overlayFormat = $imagickImage->getImageFormat();
        if ($format == 'GIF') {
            $frames = $this->image->coalesceImages();
            if ($overlayFormat == 'GIF') {
                $overFrames = $imagickImage->coalesceImages();
            }

            foreach ($frames as $idx => $frame) {
                if ($overlayFormat == 'GIF') {
                    $frame->compositeImage($overFrames, Imagick::COMPOSITE_DEFAULT, $xVal, $yVal);
                    if (!$overFrames->hasNextImage()) {
                        $overFrames->setFirstIterator();
                    } else {
                        $overFrames->nextImage();
                    }
                } else {
                    $frame->compositeImage($imagickImage, Imagick::COMPOSITE_DEFAULT, $xVal, $yVal);
                }
            }
            $this->image = $frames->deconstructImages();
        } else {
            $this->image->compsiteImage($imagickImage, Imagick::COMPOSITE_DEFAULT, $xVal, $yVal);
        }
    }

    /**
     * Takes an Imagick drawing and overlays it on top of an image/gif.
     *
     * @param ImagickDraw $imagickDrawing - Imagick drawing to be overlayed
     *
     * @return Null
     */
    public function draw($imagickDrawing)
    {
        $format = $this->image->getImageFormat();
        if ($format == 'GIF') {
            $frames = $this->image->coalesceImages();

            foreach ($frames as $frame) {
                $frame->drawImage($imagickDrawing);
            }

            $this->image = $frames->deconstructImages();
        } else {
            $this->image->drawImage($imagickDrawing);
        }
    }

    /**
     * Saves a gif to a path.
     *
     * @param String $savePath - Path to save the gif at.
     *
     * @return Null
     */
    public function optimizeGifAndSave($savePath)
    {
        // $this->image->optimizeImageLayers();
        $this->image->writeImages($savePath, true);
    }

    /**
     * Crops to the center of the image based a percent.
     * ($percent = 0.50 means the width and height would be half the original)
     *
     * @param Int $percent - Percent of image to crop.
     * @param Boolean $resizeToOriginal - (default false) If true resizes video to original size.
     *
     * @return Null
     */
    public function cropImage($percent, $resizeToOriginal = false)
    {
        $oriHeight = $this->image->getImageHeight();
        $oriWidth = $this->image->getImageWidth();

        $x_offset = $percent * $oriWidth;
        $width = $oriWidth - ($x_offset * 2);
        $y_offset = $percent * $oriHeight;
        $height = $oriHeight - ($y_offset * 2);

        $this->image->cropImage($width, $height, $x_offset, $y_offset);
        if ($resizeToOriginal) {
            $this->image->resizeImage($oriWidth, $oriHeight, Imagick::FILTER_LANCZOS, 1, true);
        }
    }
}
