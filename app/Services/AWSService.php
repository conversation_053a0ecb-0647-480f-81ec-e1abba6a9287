<?php namespace App\Services;

use Config;
use Exception;
use File;
use Illuminate\Support\Facades\Log;
use Storage;

use Bugsnag\BugsnagLaravel\Facades\Bugsnag;

class AWSService
{

    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct($storage = null)
    {
        $this->storage = $storage ?: Storage::disk(Config::get('filesystems.default'));
    }

    public function put($path, $file, $params, $tries = 0)
    {
        try {
            $this->storage->put($path, $file, $params);

            if (!$this->has($path)) {
                throw new \Exception('Uploaded file not detected: ' . $path);
            }
        } catch (Exception $e) {
            if ($tries < 3) {
                sleep(1);
                $this->put($path, $file, $params, $tries + 1);
            } else {
                Bugsnag::notifyException(new \RuntimeException("Error communicating with AWS"));
            }
        }
    }

    public function get($path, $tries = 0)
    {
        try {
            return $this->storage->get($path);
        } catch (\Exception $e) {
            if ($tries < 3) {
                sleep(2);
                return $this->get($path, $tries + 1);
            } else {
                Bugsnag::notifyException(new \RuntimeException("Error communicating with AWS"));
            }
        }
    }

    public function putDir($prodPath, $localPath, $params, $tries = 0)
    {
        $files = File::files($localPath);
        try {
            if ($this->storage->exists($prodPath)) {
                $this->storage->deleteDirectory($prodPath);
            }
            $this->storage->createDirectory($prodPath);
        } catch (\Exception $e) {
            if ($tries <3) {
                sleep(1);
                return $this->putDir($prodPath, $localPath, $params, $tries+1);
            } else {
                Bugsnag::notifyException(new \RuntimeException("Error communicating with AWS"));
            }
        }


        foreach ($files as $file) {
            $filename = basename($file);

            $path = $prodPath . $filename;

            $this->put($path, file_get_contents($file), $params);
        }
    }

    public function has($filePath)
    {
        return $this->storage->has($filePath);
    }
}