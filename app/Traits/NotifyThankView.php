<?php

namespace App\Traits;

use App\Observers\ThankViewObserver;

trait NotifyThankView
{
    public static function bootNotifyThankView()
    {
        // This makes it easy to toggle the search feature flag
        // on and off. This is going to prove useful later on
        // when deploy the new search engine to a live app.
        if (config('services.thankview.enabled')) {
            static::observe(ThankViewObserver::class);
        }
    }

    public function getThankViewArray()
    {
        $metadata = json_decode($this['metadata']);
        $uuid = isset($metadata->uuid) ? $metadata->uuid : null;
        $data = [
            'uuid' => $metadata->uuid,
        ];
        
        if (isset($metadata->user_id)) {
            $data['user_id'] = intval($metadata->user_id);
        }

        foreach ($this->thankViewData as $trait) {
            $data[$trait] = $this[$trait];
        }

        return $data;
    }
}
