<?php

namespace App\Providers;

use App\Services\ThankViewService;
use Illuminate\Support\ServiceProvider;

class ThankViewServiceProvider extends ServiceProvider
{
    /**
     * Indicates if loading of the provider is deferred.
     *
     * @var bool
     */
    protected $defer = true;

    /**
     * Bootstrap the application services.
     *
     * @return void
     */
    public function boot()
    {
        //
    }

    /**
     * Register the application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->bind(ThankViewService::class, function ($app) {
            $url = config('services.thankview.url');
            $header = ['Api-Gateway-Token' => config('services.thankview.token')];
            $prod = env('APP_ENV') === 'production';
            return new ThankViewService($url, $header, $prod);
        });
    }

    /**
     * Get the services provided by the provider.
     *
     * @return array
     */
    public function provides()
    {
        return [ThankViewService::class];
    }
}
