<?php

namespace App\Observers;

use App\Services\ThankViewService;

class ThankViewObserver
{
    private $thankViewService;

    public function __construct(ThankViewService $thankViewService)
    {
        $this->thankViewService = $thankViewService;
    }

    public function created($model)
    {
        if (!config('services.thankview.enabled')) {
            return;
        }

        $data = $model->getThankViewArray();
        if (!$data['uuid']) {
            return;
        }

        $this->thankViewService->store([
            'table' => $model->getTable(),
            'id' => $model->getKey(),
            'data' => $data
        ]);
    }

    public function updated($model)
    {
        if (!config('services.thankview.enabled')) {
            return;
        }
        
        $data = $model->getThankViewArray();
        if (!$data['uuid']) {
            return;
        }

        $this->thankViewService->update([
            'table' => $model->getTable(),
            'id' => $model->getKey(),
            'data' => $data
        ]);
    }
}
