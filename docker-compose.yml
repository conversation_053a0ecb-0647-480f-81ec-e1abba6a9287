services:
  laravel:
    # we want to use the image which is build from our Dockerfile
    build:
      context: .
    # apache is running on port 80 and we expose it to port 80 on our host machine
    ports:
      - 82:80
      - 4432:443
    # attach current directory to application directory in docker
    volumes:
      - ./:/var/www/html
      - ./docker/httpd-vhosts.conf:/etc/apache2/sites-available/000-default.conf
    container_name: dev-builder-thankview.com
  # we use this service to easily view our database on localhost:8081
  phpmyadmin:
    restart: always
    image: phpmyadmin/phpmyadmin
    ports:
      - 8082:80
    environment:
      - PMA_HOST=mysql_eb
      - PMA_USER=root
      - PMA_PASSWORD=root
    depends_on:
      - mysql_eb
  mysql_eb:
    platform: linux/amd64
    # we use the mysql base image, version 5.7.26
    image: mysql:5.7
    # we mount a datavolume to make sure we don't lose data
    ports:
      - 33062:3306
    volumes:
       - db_data:/var/lib/mysql
       - ./docker/init_db:/docker-entrypoint-initdb.d
    # setting some envvars to create the DB
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=builders
      - MYSQL_ALLOW_EMPTY_PASSWORD=yes
volumes:
  db_data:
  data:
    driver: local
networks:
  default:
    external:
      name: thankview-app_net

