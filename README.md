# ThankView Envelope Builder

The ThankView Envelope Builder is a standalone Vue / Lumin application for generating simple envelope designs for ThankView customers. Images to support envelopes in the main ThankView application are generated by this tool, and sent to the main ThankView application for use.


## Initial Setup

*These setup steps assume you have the same command line aliases recommended by the main ThankView application readme.

1. Build Docker images: `dc build` (testing10)
2. Start Docker containers: `dc up -d`
3. Install dependencies with composer: `dcl composer update`
4. Create a copy of `.npmrc.example` as `.npmrc`
    - Get the ThankView Application Token from the shared npm login in 1Password and add it to the file
5. Install front end dependencies: `npm i`
6. Add entry to `/etc/hosts`: 127.0.0.1 dev-builder-thankview.com

## Setup Application State
1. Create a copy of `public/.htaccess.example` as `public/.htaccess`
2. Create a copy of `.env.example` as `.env`
   * Paste in the `THANKVIEW_API_TOKEN`
   * Paste in the `AWS_KEY` and `AWS_SECRET`
   * Paste in the `BUGSNAG_API_KEY` from 1password 
   * If you need to test with envelopes stored in AWS, change the following variables:
      + `ENVELOPE_PATH=testing-envelopes/`
      + `ENVELOPE_ASSETS_PATH=https://builder-assets.thankview.com/testing-envelopes/`
      + `FILESYSTEM_DRIVER=s3`
   * Copy the absolute path of your builder's `public` folder and set as `APP_PATH`. (e.g. /Users/<USER>/Documents/thankview-envelope-builder/public)
3. Create Database table
   * Run your migration files by using the command `dcla migrate`
   * Seed your database by running the command `dcla db:seed`
   * You can run other artisan commands in similar ways
4. If you are working on the integration with `thankview-app`, change `UPDATE_THANKVIEW` to `true`.

## Working in Your Local Environment

1. Start the docker containers in `thankview-app` to create the docker network we will need to connect to. Use `dc up -d`
2. Build the frontend with webpack: `npm run dev`
3. Visit the builder in your browser: https://dev-builder-thankview.com:4432

## Deployment (subdomain.buildtest-thankview.com)

Visit our [testing deployment notion page](https://www.notion.so/thankview/Deploying-to-a-Testing-Server-454cf3bd41b144b596c6bd394f59d1ef) to deploy your branch to a new testing server instance. Use <EMAIL> with password 'test' to login.

## Deployment (production)
1. Run `envoy run deploy-builder-prod` to deploy git changes to all production servers
2. Run `npm run prod` to deploy js, css to staging
