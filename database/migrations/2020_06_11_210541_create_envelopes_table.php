<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateEnvelopesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('envelopes', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->nullable(false);
            $table->string('slug')->nullable(false);
            $table->string('primary_color')->default('#FFFFFF')->nullable(false);
            $table->string('secondary_color')->default('#000000')->nullable(false);
            $table->string('tertiary_color')->nullable(); 

            $table->string('envelope_color')->default('#808080')->nullable(false);
            $table->string('envelope_text_color')->default('#FFFFFF')->nullable(false);
            $table->string('liner_color')->default('#FFFFFF')->nullable(false);
            $table->string('postmark_copy')->nullable();
            $table->string('path');

            $table->string('color_1')->nullable();
            $table->string('color_2')->nullable();
            $table->string('color_3')->nullable();

            $table->string('metadata')->nullable();
            $table->timestamps();
            $table->integer('design_id')->unsigned()->nullable();
            $table->softDeletes('deleted_at', 0);

        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('envelopes');
    }
}
