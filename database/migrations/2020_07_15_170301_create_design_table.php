<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateDesignTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('designs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name');
            $table->integer('num_of_colors');
            $table->string('category');
            $table->integer('order'); 
            $table->timestamps();
            $table->softDeletes('deleted_at', 0);
        });

        Schema::table('envelopes', function (Blueprint $table) {
            $table->foreign('design_id')
                ->references('id')
                ->on('designs');
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('envelopes', function (Blueprint $table) {
            $table->dropForeign('envelopes_design_id_foreign');
            $table->dropColumn('design_id');
        });

        Schema::dropIfExists('designs');
    }
}
