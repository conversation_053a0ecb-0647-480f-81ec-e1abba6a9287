<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EnvelopesFormInputs extends Migration
{
    /**
     * Run the migrations.
     *
     * Saves previously missing form inputs from the frontend
     * Allows the frontend state to be recreated
     *
     * @return void
     */
    public function up()
    {
        Schema::table('envelopes', function (Blueprint $table) {
            $table->boolean('has_front_logo');
            $table->boolean('has_back_logo');
            $table->boolean('has_stamp_image');
            $table->float('stamp_zoom_level')->default(1); 
            $table->string('postmark_color')->default('none');
            $table->boolean('is_editable')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('envelopes', function (Blueprint $table) {
            $table->dropColumn('is_editable');
            $table->dropColumn('postmark_color');
            $table->dropColumn('stamp_zoom_level');
            $table->dropColumn('has_stamp_image');
            $table->dropColumn('has_back_logo');
            $table->dropColumn('has_front_logo');
        });
    }
}
