<?php

/** @var \Illuminate\Database\Eloquent\Factory $factory */

use App\Envelope;
use Faker\Generator as Faker;

/*
|--------------------------------------------------------------------------
| Model Factories
|--------------------------------------------------------------------------
|
| This directory should contain each of the model factory definitions for
| your application. Factories provide a convenient way to generate new
| model instances for testing / seeding your application's database.
|
*/

$factory->define(Envelope::class, function (Faker $faker) {
    return [
        'primary_color' => $faker->hexcolor,
        'secondary_color' => $faker->hexcolor,
        'tertiary_color' => $faker->hexcolor,
        'envelope_color' => $faker->hexcolor,
        'liner_color' => $faker->hexcolor,
        'postmark_copy' => $faker->sentence,
        'swoops' => false,
        'stripes' => false,
        'color_1' => $faker->hexcolor,
        'color_2' => $faker->hexcolor,
        'color_3' => $faker->hexcolor,
        'airmail_stripe' => $faker->hexcolor
    ];
});
