<?php

use Illuminate\Database\Seeder;

use App\Design;

class DesignTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $designs = [
            [
                'name' => 'Single Stripe',
                'num_of_colors' => 1,
                'category' => 'Stripes',
                'order' => 1
            ],
            [
                'name' => 'Double Stripes',
                'num_of_colors' => 2,
                'category' => 'Stripes',
                'order' => 2
            ],
            [
                'name' => 'Triple Stripes',
                'num_of_colors' => 3,
                'category' => 'Stripes',
                'order' => 3
            ],
            [
                'name' => 'Air Mail Stripe',
                'num_of_colors' => 2 ,
                'category' => 'Stripes',
                'order' => 4
            ],
            [
                'name' => 'Single Swoop',
                'num_of_colors' => 1,
                'category' => 'Swoops',
                'order' => 1
            ],
            [
                'name' => 'Double Swoop',
                'num_of_colors' => 2,
                'category' => 'Swoops',
                'order' => 2
            ]
        ];

        foreach($designs as $design) 
        {
            if (!Design::whereName($design['name'])->first()) {
                Design::create($design);
            }
        }

    }
}
