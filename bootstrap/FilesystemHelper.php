<?php

use Illuminate\Support\HtmlString;

if (!function_exists('img_path')) {
    function img_path()
    {
        return 'img';
    }
}

if (!function_exists('media_path')) {
    function media_path()
    {
        return env('APP_CDN_HOST') ?: (env('APP_MEDIA_HOST') . '/');
    }
}

if (!function_exists('static_asset_path')) {
    function static_asset_path()
    {
        return env('ASSETS_PATH', 'https://builder-assets.thankview.com/');
    }
}

if (!function_exists('temp_path')) {
    function temp_path()
    {
        return storage_path() . '/tmp/';
    }
}
if (!function_exists('public_path')) {
    /**
     * Return the path to public dir
     *
     * @param null $path
     *
     * @return string
     */
    function public_path($path = null)
    {
        return rtrim(app()->basePath('public/' . $path), '/');
    }
}

if (! function_exists('mix')) {
    /**
     * Get the path to a versioned Mix file.
     *
     * @param string $path
     * @param string $manifestDirectory
     * @return \Illuminate\Support\HtmlString
     *
     * @throws \Exception
     */
    function mix($path, $manifestDirectory = 'build')
    {
        static $manifests = [];
        if (substr($path, 0, 1) != '/') {
            $path = "/${path}";
        }
        if ($manifestDirectory && substr($manifestDirectory, 0, 1) != '/') {
            $manifestDirectory = "/${manifestDirectory}";
        }

        if (file_exists(public_path($manifestDirectory.'/hot'))) {
            $url = rtrim(file_get_contents(public_path($manifestDirectory.'/hot')));
            $prefix = explode(":", $url, 2)[0];
            if ($prefix == "http" || $prefix == "https") {
                $returnUrl = substr($url, strpos($url, ":") + 1);
                return new HtmlString($returnUrl.$path);
            }

            return new HtmlString("//localhost:8080{$path}");
        }

        $manifestPath = public_path($manifestDirectory.'/mix-manifest.json');

        if (! isset($manifests[$manifestPath])) {
            if (! file_exists($manifestPath)) {
                throw new Exception('The Mix manifest does not exist.');
            }

            $manifests[$manifestPath] = json_decode(file_get_contents($manifestPath), true);
        }

        $manifest = $manifests[$manifestPath];

        if (! isset($manifest[$path])) {
            throw new Exception("Unable to locate Mix file: {$path}.");
        }

        return new HtmlString($manifestDirectory.$manifest[$path]);
    }
}
