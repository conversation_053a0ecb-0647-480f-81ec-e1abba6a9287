let mix = require('laravel-mix');
require('laravel-mix-purgecss');
const path = require('path');

mix.setPublicPath('public/build');
mix.js('resources/app.js', '.');
mix.sass('resources/scss/app.scss', '.')
    .purgeCss({
        extend: {
            content: [
                'resources/components/**/*.vue',
                'resources/views/**/*.blade.php',
                'node_modules/@evertrue/tv-components/**/*.vue',
            ],
            whitelist: ['char-count'],
        }
    });
mix.options({
    hmrOptions: {
        host: 'dev-builder-thankview.com',
        port: 4433,
    }
});
mix.webpackConfig({
    output: {
        publicPath: 'build/',
        chunkFilename: '[name].js'
    },
});