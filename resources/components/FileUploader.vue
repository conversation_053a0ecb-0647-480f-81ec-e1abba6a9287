<template>
    <div class="file-uploader">
        <input
            :id="fileUploadId"
            :ref="fileUploadId"
            class="file-uploader__input none"
            type="file"
            text="choose file to upload"
            @change="handleSelectedFile"
        >
        <label
            v-if="!newFileData"
            :for="fileUploadId"
            class="
                flex
                justify-content-center
                align-items-center
                bg-white
                font-gray-45
                cursor-pointer
            "
            @drop.prevent="handleSelectedFile"
            @dragover.prevent
        >
            <p class="text-align-center">
                <i class="fas fa-cloud-upload font-size-48px margin-b-4px" />
                <br>Drag an image to upload
            </p>
        </label>
        <div class="width-100 text-align-center">
            <img
                :id="`${fileUploadId}-preview`"
                :ref="`${fileUploadId}-preview`"
                v-if="newFileData && !hidePreview"
                :src="newFileData"
                class="file-uploader__preview-image"
            >
        </div>
        <div class="file-uploader__error-messages">
            <p v-if="fileError">
                {{ fileError }}
            </p>
            <slot name="error-messages" />
        </div>
        <button
            class="btn btn--primary-alt width-100 margin-t-16px"
            @click.prevent="selectFile"
        >
            Choose File
        </button>
    </div>
</template>

<script>
export default {
    props: {
        fileUploadId: String,
        hidePreview: Boolean,
        newFileData: String,
    },

    data() {
        return {
            fileError: false,
            fileUploading: false,
        };
    },

    methods: {
        selectFile() {
            this.$emit('reportEvent', 'file-clicked');
            this.$refs[this.fileUploadId].click();
        },

        handleSelectedFile($event) {
            let files;

            if (
                // if drag and dropped
                $event.dataTransfer &&
                $event.dataTransfer.files &&
                $event.dataTransfer.files[0]
            ) {
                this.$emit('reportEvent', 'file-dropped'); // log event that user drag & dropped file
                files = $event.dataTransfer.files;
            } else if (
                // if selected via clicking on input label or button
                this.$refs[this.fileUploadId] &&
                this.$refs[this.fileUploadId].files &&
                this.$refs[this.fileUploadId].files[0]
            ) {
                files = this.$refs[this.fileUploadId].files;
            } else {
                return;
            }

            const validTypes = ['image/png', 'image/jpeg', 'image/gif'];

            if (validTypes.indexOf(files[0].type) >= 0) {
                const reader = new FileReader();
                reader.onload = e => {
                    const newImageData = e.target.result;
                    this.$emit('setImage', this.fileUploadId, newImageData);
                };
                reader.readAsDataURL(files[0]);
                this.fileError = '';

                // upload
            } else {
                this.fileError = 'Upload a PNG, JPEG, of GIF file.';
            }
        },
    },
};
</script>