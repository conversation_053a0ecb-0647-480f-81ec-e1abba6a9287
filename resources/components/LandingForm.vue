<template>
    <div
        ref="options"
        class="options pad-r-40px pad-l-40px overflow-x-hidden overflow-y-auto"
    >
        <div
            v-if="submissionError && submissionErrorMsg"
            class="bg-red-90 border-radius-6px pad-t-4px pad-r-8px pad-b-4px pad-l-8px margin-t-4px margin-b-4px"
        >
            <p
                class="font-red-45 margin-t-0px margin-b-0px"
            >
                <i class="fas fa-exclamation-circle" />
                {{ submissionErrorMsg }}
            </p>
        </div>
        <form
            ref="landingProps"
            name="landingProps"
            class="collapsible-form pad-b-32px"
        >
            <div class="open pad-b-32px">
                <h2
                    class="cursor-pointer font-charcoal font-size-20px font-w-5 pad-t-16px pad-b-16px margin-t-0px margin-b-0px"
                    @click="toggleSection"
                >
                    Landing Page Title
                    <i class="fas fa-chevron-down float-right pointer-events-none" />
                </h2>
                <label
                    for="landing-title"
                    class="block width-100 font-charcoal font-w-7 margin-b-8px required"
                >
                    Use a memorable name to find it quickly
                </label>
                <div class="build-form__input-wrapper">
                    <input
                        id="landing-title"
                        v-model="landingProps.name"
                        type="text"
                        maxlength="20"
                        placeholder="Untitled Landing Page"
                    >
                </div>
                <div class="flex">
                    <p
                        v-if="submissionError && !valid.landingProps.name.required"
                        class="font-red-45 margin-t-4px margin-b-0px"
                    >
                        <i class="fas fa-exclamation-circle" />
                        Give your landing page a title.
                    </p>
                    <p
                        v-if="submissionError && !valid.landingProps.name.maxLength"
                        class="font-red-45 margin-t-4px margin-b-0px"
                    >
                        <i class="fas fa-exclamation-circle" />
                        Title must be no more than 20 characters
                    </p>
                    <p class="char-count">
                        {{ landingProps.name.length }} / 20
                    </p>
                </div>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="cursor-pointer font-charcoal font-size-20px font-w-5 pad-t-16px pad-b-16px margin-t-0px margin-b-0px"
                    @click="toggleSection"
                >
                    Navigation Bar
                    <i class="fas fa-chevron-down float-right pointer-events-none" />
                </h2>
                <p class="relative margin-t-0px">
                    Style your landing page with branded elements
                </p>
                <hex-color-input
                    v-model="landingProps.header_bkgd_color"
                    class="margin-t-32px margin-b-32px"
                    :name="'header_bkgd_color'"
                    @input="setDisplayColor"
                >
                    Navigation Bar Color
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.header_bkgd_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
                <label
                    for="header_logo"
                    class="block width-100 font-charcoal font-w-7 margin-b-8px"
                >
                    Organization Logo
                </label>
                <p>Add your logo to the top left of the navigation bar (we recommend a transparent logo)</p>
                <file-uploader
                    class="margin-t-8px"
                    :file-upload-id="'header_logo_file'"
                    :new-file-data="landingProps.header_logo_file"
                    @setImage="setImage"
                    @reportEvent="reportLogoEvent"
                />
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="cursor-pointer font-charcoal font-size-20px font-w-5 pad-t-16px pad-b-16px margin-t-0px margin-b-0px"
                    @click="toggleSection"
                >
                    Your Background
                    <i class="fas fa-chevron-down float-right pointer-events-none" />
                </h2>
                <p class="relative margin-t-0px">
                    Place an image behind your video to set a tone for your personalized video
                </p>
                <p
                    v-if="bkgdImgDeleted"
                    class="font-red-45 margin-b-16px"
                >
                    <i class="fas fa-exclamation-circle" />
                    The current background image used in this design has been deleted. If you select a new image and save this design, you will not be able to select the image again.
                </p>
                <div class="flex" style="justify-content: space-between;">
                    <button
                        type="button"
                        class="btn btn--primary-alt"
                        @click="openBackgroundUploader()"
                    >
                        Upload Background
                    </button>
                    <button
                        type="button"
                        class="btn btn--primary-alt"
                        @click="() => {
                            $emit('updatePreviewProp', 'hide_message_gradient', !landingProps.hide_message_gradient);
                        }"
                    >
                        {{ landingProps.hide_message_gradient ? 'Show Gradient' : 'Hide Gradient' }}
                    </button>
                </div>
                <div class="landing-form__bg-img-list margin-t-16px">
                    <asset-tile
                        v-for="image in bkgdImages"
                        :key="image.id"
                        :asset="image"
                        :editable="true"
                        @edit-asset="openBackgroundUploader"
                        @delete-asset="deleteBackgroundImage"
                        @select-asset="selectBackgroundImage"
                    />
                </div>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="cursor-pointer font-charcoal font-size-20px font-w-5 pad-t-16px pad-b-16px margin-t-0px margin-b-0px"
                    @click="toggleSection"
                >
                    Button Colors
                    <i class="fas fa-chevron-down float-right pointer-events-none" />
                </h2>
                <p class="relative margin-t-0px">
                    Use bold colors to grab your recipients’ attention and further engage with your organization
                </p>
                <hex-color-input
                    v-model="landingProps.button_text_color"
                    class="margin-t-32px"
                    :name="'button_text_color'"
                    @input="setDisplayColor"
                >
                    Call to Action Text
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.button_text_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
                <hex-color-input
                    v-model="landingProps.button_color"
                    class="margin-t-32px"
                    :name="'button_color'"
                    @input="setDisplayColor"
                >
                    Call to Action Button
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.button_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
                <hex-color-input
                    v-model="landingProps.button_below_text_color"
                    class="margin-t-32px"
                    :name="'button_below_text_color'"
                    @input="setDisplayColor"
                >
                    Secondary Button Text
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.button_below_text_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
                <hex-color-input
                    v-model="landingProps.button_reply_color"
                    class="margin-t-32px"
                    :name="'button_reply_color'"
                    @input="setDisplayColor"
                >
                    Reply Button
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.button_reply_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
                <hex-color-input
                    v-model="landingProps.button_save_color"
                    class="margin-t-32px"
                    :name="'button_save_color'"
                    @input="setDisplayColor"
                >
                    Save Button
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.button_save_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
                <hex-color-input
                    v-model="landingProps.button_share_color"
                    class="margin-t-32px"
                    :name="'button_share_color'"
                    @input="setDisplayColor"
                >
                    Share Button
                    <template v-slot:error-message>
                        <div
                            v-if="!valid.landingProps.button_share_color.isHex"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Please enter a valid hex code.
                        </div>
                    </template>
                </hex-color-input>
            </div>
        </form>
    </div>
</template>

<script>
import { AssetTile } from '@evertrue/tv-components';
import FileUploader from './FileUploader';
import HexColorInput from './HexColorInput';

export default {

    components: {
        'asset-tile': AssetTile,
        'file-uploader': FileUploader,
        'hex-color-input': HexColorInput,
    },
    props: {
        bkgdImages: Array,
        bkgdImgDeleted: {
            type: Boolean,
            default: false,
        },
        landingProps: Object,
        valid: Object,
        submissionError: Boolean,
        submissionErrorMsg: {
            default: null,
            type: String,
        }
    },
    // populate the form with defaults
    data() {
        return {

        };
    },

    computed: {

    },

    watch: {
        submissionError(newVal) {
            // scroll to first input error
            if (newVal === true) {
                setTimeout(() => {
                    const inputErrors = document.getElementsByClassName('font-red-45');
                    if (inputErrors.length) {
                        const firstInputError = inputErrors[0];
                        this.$refs.options.scrollTop = firstInputError.offsetTop - 350;
                    }
                }, 10);
            }
        },
    },

    methods: {
        setDisplayColor($event, propKey) {
            let val = $event.target.value;
            // inject # if it's not entered
            if (val.length > 0 && val.charAt(0) !== '#') {
                val = '#' + val;
            }
            this.$emit('updatePreviewProp', propKey, val);
        },

        setImage(propKey, imageData) {
            this.$emit('updatePreviewProp', propKey, imageData);

            if (propKey === 'header_logo_file') {
                // also update header_logo prop with new image data
                this.$emit('updatePreviewProp', 'header_logo', imageData);
            }
        },

        selectBackgroundImage(image) {
            // tell parent to update preview
            this.$emit('selectBkgdImg', image);
        },

        deleteBackgroundImage(image) {
            // the select background click handler
            this.$emit('deleteBkgdImage', image);
        },

        /**
         * @param {Object} img - image object, if we're editing
         */
        openBackgroundUploader(img = null) {
            this.$emit('openNewBkgdImg', img);
        },

        /**
         * Event handler for file events on the logo uploader. Maps event
         * shorthand names to the full event name requested.
         *
         * @param {String} eventName - shorthand event name to report on
         */
        reportLogoEvent(eventName) {
            // map event short hand to real event names before passing to parent to report
            switch(eventName) {
                case 'file-clicked':
                    eventName = 'Upload Logo - Clicked';
                    break;
                case 'file-dropped':
                    eventName = 'Upload Logo - Dragged';
                    break;
                default:
                // nothing
            }
            this.$emit('reportEvent', eventName);
        },

        toggleSection: function(event) {
            // if we want to animate this we'll need some more complicated js
            // but for now, just a simple class toggle is good
            const section = event.target.parentElement;
            section.classList.toggle('open');
        },
    },
};
</script>
