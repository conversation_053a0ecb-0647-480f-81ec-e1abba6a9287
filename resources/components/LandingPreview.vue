<template>
    <div
        class="
            preview-area
            flex flex-dir-col
            justify-content-space-between
            bg-white
            max-width-100
            overflow-hidden
            pad-t-40px pad-r-40px pad-b-40px pad-l-40px
        "
    >
        <div class="landing-preview__size-controls flex align-items-center justify-content-end margin-b-16px">
            <h2 class="font-serif font-size-24px font-blue-16 margin-t-0px margin-r-auto margin-b-0px">
                {{ landingProps.name || 'Untitled Landing Page' }}
            </h2>
            <p class="inline-block font-size-16px font-gray-40 margin-t-0px margin-r-16px margin-b-0px">
                Preview as:
            </p>
            <button
                class="btn btn--icon-only btn--circle bg-gray-90 margin-r-8px"
                :aria-selected="size === 'desktop'"
                @click="setPreviewSize('desktop')"
            >
                <i class="fa fa-desktop" />
                <span class="btn__tooltip btn__tooltip--up font-white">Desktop</span>
            </button>
            <button 
                class="btn btn--icon-only btn--circle bg-gray-90 margin-r-8px"
                :aria-selected="size === 'tablet'"
                @click="setPreviewSize('tablet')"
            >
                <i class="fa fa-tablet-alt" />
                <span class="btn__tooltip btn__tooltip--up font-white">Tablet</span>
            </button>
            <button
                class="btn btn--icon-only btn--circle bg-gray-90"
                :aria-selected="size === 'mobile'"
                @click="setPreviewSize('mobile')"
            >
                <i class="fa fa-mobile-alt" />
                <span class="btn__tooltip btn__tooltip--up font-white">Mobile</span>
            </button>
        </div>
        <div
            class="landing-preview__container relative width-100 overflow-auto text-align-center pad-b-32px margin-center margin-b-32px"
            :class="{ 'landing-preview__container--tablet' : size === 'tablet', 'landing-preview__container--mobile' : size === 'mobile' }"
            :data-size="size"
            :style="landingProps.hide_message_gradient && landingProps.bkgd_image ? { backgroundImage: `url(${landingProps.bkgd_image.bkgd_image})`, backgroundPosition: 'top center', backgroundSize: 'cover', backgroundRepeat: 'no-repeat' } : {}"
        >
            <div
                class="landing-preview__bg-img absolute z-idx-1 width-100"
                v-if="!landingProps.hide_message_gradient"
                :style="{ backgroundImage: landingProps.bkgd_image ? `url(${landingProps.bkgd_image.bkgd_image})` : '' }"
            />
            <div
                class="landing-preview__header sticky top-0 flex align-items-center"
                :style="{ backgroundColor: landingProps.header_bkgd_color || '#fff' }"
            >
                <img
                    :src="headerLogoDisplay"
                    alt=""
                    role="presentation"
                    class="landing-preview__header-logo"
                >
            </div>
            <div class="landing-preview__content relative overflow-hidden z-idx-2">
                <div v-if="!landingProps.hide_message_gradient" class="landing-preview__content-gradient" />
                <div class="landing-preview__video-final">
                    <div class="landing-preview__player">
                        <div class="landing-preview__player-play">
                            <i class="fa fa-play" aria-hidden="true" />
                        </div>
                    </div>
                    <div
                        class="btn btn--primary"
                        :style="{
                            backgroundColor: landingProps.button_color,
                            color: landingProps.button_text_color,
                        }"
                    >
                        CTA Button
                    </div>
                    <div class="landing-preview__text-container">
                        <p class="pad-r-24px pad-l-24px">
                            This is where your amazing, awesome, stunning, fascinating, wonderful, and prodigious (can we think of any more synoyms?) personalized message will go. Tell your recipients that they're the greatest folks out there, just like yourself! Use merge fields to really amp up the personalization and keep the fun times rolling. Did we mention that we love unicorns? Fun fact: when a unicorn has wings, they are called Pegasi!
                        </p>
                    </div>
                    <hr class="max-width-200px">
                    <div
                        class="landing-preview__button-below inline-block margin-t-24px"
                    >
                        <div
                            class="btn btn--secondary"
                            :style="{
                                backgroundColor: landingProps.button_reply_color,
                                color: landingProps.button_below_text_color,
                            }"
                        >
                            Reply
                        </div>
                        <div
                            class="btn btn--primary margin-r-4px margin-l-4px"
                            :style="{
                                backgroundColor: landingProps.button_save_color,
                                color: landingProps.button_below_text_color,
                            }"
                        >
                            Save
                        </div>
                        <div
                            class="btn btn--primary-alt"
                            :style="{
                                backgroundColor: landingProps.button_share_color,
                                color: landingProps.button_below_text_color,
                            }"
                        >
                            Share
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <step-navigation
            :createNewAssetButtonTitle="'Create New Landing Page'"
            :existingLandingIdString="this.existingLandingIdString"
            :navigationType="'landing'"
            :step="this.step"
            :submitting="this.submitting"
            :parentPage="this.parentPage"
            @changeStep="changeStep"
            @saveLanding="saveLanding"
            @newLanding="newLanding"
        />
    </div>
</template>

<script>
import StepNavigation from './StepNavigation.vue';
export default {
    components: { StepNavigation },
    props: {
        landingProps: Object,
        valid: Object,
        submitting: Boolean,
        step: Number,
        existingLandingIdString: String,
        parentPage: String,
        hide_message_gradient: {
            type: Boolean,
            default: false
        },
    },

    data() {
        return {
            size: 'desktop'
        };
    },

    computed: {
        headerLogoDisplay() {
            // if we have neither a logo file, or a saved logo image, return an empty string
            // this occurs when creating a brand new page
            // if we don't return an empty string, we end up trying to request
            // a malformed url, resulting in an uncaught error at render
            if (this.landingProps.header_logo === null && this.landingProps.header_logo_file === null) {
                return '';
            }
            // newly uploaded image data takes priority
            if (this.landingProps.header_logo_file) {
                return this.landingProps.header_logo_file;
            } else {
                // work out the existing image path
                // local and qa environments save these images in the main app public directory
                // in prod we save the full url and don't need this prefix so we default to empty string
                let imgPathPrefix = '';
                if (config.env !== 'production') {
                    // sometimes the env variable includes protocol, sometimes it doesn't, check for it
                    imgPathPrefix = `${config.tvMainAppUrl.indexOf('http') !== -1 ? '' : 'https://'}${config.tvMainAppUrl}`;
                }
                return `${imgPathPrefix}${this.landingProps.header_logo}`;
            }
        }
    },

    watch: {
        landingProps: {
            handler: function() {
                // probably update preview?
            },
            deep: true,
        },
    },

    mounted() {
        // do things
    },

    methods: {
        changeStep(stepNumber) {
            this.$emit('changeStep', stepNumber);
        },

        setPreviewSize(size) {
            this.size = size;
        },

        saveLanding() {
            this.$emit('saveLanding');
        },

        newLanding() {
            this.$emit('newLanding');
        }
    },
};
</script>