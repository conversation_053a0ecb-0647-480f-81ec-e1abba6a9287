<template>
    <div
        class="flex"
        :class="{
            'justify-content-end': step === 1,
            'margin-t-auto': step === 1,
            'flex-100': step === 1,
            'justify-content-space-between': step > 1,
            'width-100': step > 1
        }"
    >
        <button
            href
            type="button"
            class="btn btn--secondary font-no-underline height-100"
            @click.prevent="$emit('changeStep', step - 1)"
            v-if="step > 1"
        >
            <i class="fas fa-arrow-left" />
            Back
        </button>
        <button
            href
            type="button"
            class="btn btn--primary font-no-underline align-self-flex-end"
            @click.prevent="$emit('changeStep', 2)"
            v-if="step === 1"
        >
            Continue
            <i class="fas fa-arrow-right margin-l-4px" />
        </button>
        <a
            :href="livePreviewUrl"
            type="button"
            target="_blank"
            rel="noopener noreferrer"
            class="btn btn--primary-alt font-no-underline height-100"
            v-if="step === 3 && parentPage !== 'personal-video Page'"
        >
            <i class="fas fa-eye margin-r-4px"></i>Live Preview
        </a>
        <button
            href
            type="button"
            class="btn btn--primary font-no-underline height-100"
            :disabled="processingImages || submitting"
            @click.prevent="$emit('saveEnvelope')"
            v-if="step === 2 && navigationType === 'envelope'"
        >
            {{
                !processingImages && !submitting
                    ? 'Save and Continue'
                    : 'Processing...'
            }}
            <i class="fas fa-arrow-right margin-l-4px" />
        </button>
        <button
            href
            type="button"
            class="btn btn--primary height-100"
            :disabled="submitting"
            @click.prevent="$emit('saveLanding')"
            v-if="step === 2 && navigationType === 'landing'"
        >
            {{ !submitting ? 'Save and Continue' : 'Processing...' }}
            <i class="fas fa-arrow-right margin-l-4px" />
        </button>
        <button
            href
            type="button"
            class="btn btn--primary font-no-underline height-100"
            @click.prevent="$emit('restart')"
            v-if="step === 3 && navigationType === 'envelope' && inIframe"
        >
            {{ createNewAssetButtonTitle }}
        </button>
        <button
            href
            type="button"
            class="btn btn--primary height-100"
            @click.prevent="$emit('newLanding')"
            v-if="step === 3 && navigationType === 'landing'"
        >
            {{ createNewAssetButtonTitle }}
        </button>
    </div>
</template>

<script>
export default {
    props: {
        createNewAssetButtonTitle: String,
        envelopeSlug: String,
        existingLandingIdString: String,
        navigationType: String,
        processingImages: Boolean,
        step: Number,
        submitting: Boolean,
        parentPage: String,
    },

    data() {
        return {
            tvAppPath: process.env.MIX_THANKVIEW_APP_URL,
            configUrl: window.config.tvMainAppUrl
        };
    },

    computed: {
        inIframe() {
            return window.self !== window.top;
        },
        livePreviewUrl() {
            return this.navigationType === 'envelope' ? 
            `${this.tvAppPath}/video/envelope/${this.envelopeSlug}` 
            : `${this.configUrl}/video/landing?landing_id=${this.existingLandingIdString}`;
        }
    },

    methods: {}
}
</script>