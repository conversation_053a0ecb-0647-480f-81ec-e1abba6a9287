<template>
    <div
        class="
            preview-area
            flex flex-dir-col
            bg-white
            max-width-100
            overflow-hidden
            pad-t-40px pad-r-40px pad-b-40px pad-l-40px
        "
    >
        <div>
            <h3
                class="
                    font-size-24px font-w-4
                    margin-t-0px margin-b-0px
                    width-100
                "
                :class="{ 'font-gray-45': !name }"
            >
                {{ name || 'Untitled Envelope' }}
            </h3>
            <hr />
        </div>
        <div class="flex align-items-end width-100 flex-100 margin-b-32px">
            <img class="width-33 pad-r-16px" :src="imageExports.frontImg" />
            <img
                class="width-33 pad-l-8px pad-r-8px"
                :src="imageExports.backImg"
            />
            <img class="width-33 pad-l-16px" :src="imageExports.backFlapImg" />
        </div>
        <div class="flex justify-content-space-between width-100">
            <a
                href
                @click.prevent="changeStep(2)"
                class="btn btn--secondary font-no-underline height-100"
            >
                <i class="fas fa-arrow-left"></i>
                Back
            </a>
            <a
                :href="`${tvAppPath}/video/envelope/${envelopeSlug}`"
                target="_blank"
                class="btn btn--primary-alt font-no-underline height-100"
                v-if="parentPage !== 'personal-video Page'"
            >
                <i class="fas fa-eye margin-r-4px"></i>Live Preview
            </a>
            <a
                v-if="inIframe"
                href
                class="btn btn--primary font-no-underline height-100"
                @click.prevent="restart"
                >Create Next Envelope</a
            >
        </div>
    </div>
</template>

<script>
export default {
    props: {
        name: String,
        envelopesAvailable: Number,
        envelopeSlug: String,
        imageExports: Object,
        parentPage: String,
    },

    data() {
        return {
            tvAppPath: process.env.MIX_THANKVIEW_APP_URL,
        };
    },

    computed: {
        inIframe() {
            return window.self !== window.top;
        },
    },

    methods: {
        changeStep(stepNumber) {
            this.$emit('changeStep', stepNumber);
        },
        restart() {
            this.$emit('resetEnvelope');
            setTimeout(() => {
                this.$emit('changeStep', 2);
            }, 100);
        },
    },
};
</script>

<style lang="scss" scoped>
</style>