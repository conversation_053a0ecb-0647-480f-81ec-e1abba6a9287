<template>
    <div
        class="
            preview-area
            flex flex-dir-col
            bg-white
            max-width-100
            overflow-hidden
            pad-t-40px pad-r-40px pad-b-40px pad-l-40px
        "
    >
        <div class="flex justify-content-center height-100 margin-t-32px">
            <div class="view-select cursor-pointer margin-r-32px">
                <div
                    class="
                        flex
                        align-items-center
                        width-100
                        pad-t-8px pad-r-8px pad-b-8px pad-l-8px
                        margin-b-4px
                        border-radius-4px
                        hover:bg-blue-90
                    "
                    :class="{ 'bg-blue-90': currentView === 'front' }"
                    @click="selectEnvelopeView('front')"
                >
                    <img class="width-100" :src="imageExports.frontImg" />
                </div>
                <div
                    class="
                        flex
                        align-items-center
                        width-100
                        pad-t-8px pad-r-8px pad-b-8px pad-l-8px
                        margin-b-4px
                        border-radius-4px
                        hover:bg-blue-90
                    "
                    :class="{ 'bg-blue-90': currentView === 'back' }"
                    @click="selectEnvelopeView('back')"
                >
                    <img class="width-100" :src="imageExports.backImg" />
                </div>
                <div
                    class="
                        flex
                        align-items-center
                        width-100
                        pad-t-8px pad-r-8px pad-b-8px pad-l-8px
                        margin-b-4px
                        border-radius-4px
                        hover:bg-blue-90
                    "
                    :class="{ 'bg-blue-90': currentView === 'backFlap' }"
                    @click="selectEnvelopeView('backFlap')"
                >
                    <img class="width-100" :src="imageExports.backFlapImg" />
                </div>
            </div>
            <div
                class="
                    build-canvas__container
                    absolute
                    flex-100 flex
                    justify-content-center
                "
            >
                <canvas
                    id="main-body-front"
                    :class="{ none: currentView !== 'front' }"
                />
                <canvas
                    id="image-assets"
                    :class="{ none: currentView !== 'front' }"
                />

                <canvas
                    id="main-body-back"
                    :class="{ none: currentView !== 'back' }"
                />
                <canvas
                    id="image-assets-back"
                    :class="{ none: currentView !== 'back' }"
                />
                <!-- ONLY FOR FINAL RENDER -->
                <canvas id="back-flap-closed" class="none" />

                <canvas
                    id="liner-lower"
                    :class="{ none: currentView !== 'backFlap' }"
                />
                <canvas
                    id="main-body-back-flap"
                    :class="{ none: currentView !== 'backFlap' }"
                />
                <canvas
                    id="back-flap-opened"
                    :class="{ none: currentView !== 'backFlap' }"
                />
                <canvas
                    id="liner-upper"
                    :class="{ none: currentView !== 'backFlap' }"
                />
            </div>
            <div class="build-canvas__container absolute opacity-0 flex-100">
                <canvas id="final-canvas" />
            </div>
            <div class="build-canvas__container relative flex-100">
                <img
                    :src="
                        imageExports[
                            currentView === 'front'
                                ? 'frontWithTextPreview'
                                : currentView + 'Img'
                        ]
                    "
                    class="width-100"
                />
            </div>
        </div>
        <step-navigation
            :navigationType="'envelope'"
            :processingImages="this.processingImages"
            :step="this.step"
            :submitting="this.submitting"
            @changeStep="changeStep"
            @saveEnvelope="saveEnvelope"
        />
    </div>
</template>

<script>
import StepNavigation from './StepNavigation.vue';
export default {
    components: { StepNavigation },
    props: {
        currentView: String,
        processingImages: Boolean,
        canvasProps: Object,
        imageExports: Object,
        selectedDesign: Object,
        step: Number,
        submissionError: Boolean,
        valid: Object,
        submitting: Boolean,
    },

    data() {
        return {
            editName: false,
            envelopeClosedWidth: 1119,
            envelopeClosedHeight: 733,
            topFlapHeight: 408,
            loaded: {
                mainFrontImg: false,
                mainBackImg: false,
                mainBackFlapImg: false,
                frontLogoImg: false,
                stampFrameImg: false,
                stampImg: false,
                postmarkImg: false,
                backLogoImg: false,
                flapTextureImg: false,
            },
            timeoutRenderCurrent: null,
            timeoutRenderFinal: null,
        };
    },

    computed: {
        envelopeColor() {
            return this.canvasProps.envelope_color || '#f7f7f7';
        },
        linerColor() {
            return this.canvasProps.liner_color || '#5e6065';
        },
    },

    watch: {
        canvasProps: {
            handler: function() {
                clearTimeout(this.timeoutRenderCurrent);
                this.timeoutRenderCurrent = setTimeout(() => {
                    this.renderFront();
                    this.renderBack();
                    this.renderBackFlap();
                }, 100);
            },
            deep: true,
        },

        loaded: {
            handler: function() {
                clearTimeout(this.timeoutRenderFinal);
                this.$emit('updateData', 'processingImages', true);
                this.timeoutRenderFinal = setTimeout(() => {
                    console.log('image assets loaded, starting final renders');
                    this.renderImageExports();
                }, 400);
            },
            deep: true,
        },
    },

    mounted() {
        this.mainBodyFront = document.getElementById('main-body-front');
        this.imageAssets = document.getElementById('image-assets');
        this.mainBodyBack = document.getElementById('main-body-back');
        this.imageAssetsBack = document.getElementById('image-assets-back');
        this.backFlapClosed = document.getElementById('back-flap-closed');
        this.linerLower = document.getElementById('liner-lower');
        this.mainBodyBackFlap = document.getElementById('main-body-back-flap');
        this.backFlapOpened = document.getElementById('back-flap-opened');
        this.linerUpper = document.getElementById('liner-upper');

        this.renderFront();
        this.renderBack();
        this.renderBackFlap();
    },

    methods: {
        toggleEditEnvelope() {
            this.editName = !this.editName;
            this.$nextTick(() => {
                if (this.editName) {
                    this.$refs.nameInput.focus();
                } else {
                    this.$emit(
                        'updateCanvasProp',
                        'name',
                        this.canvasProps.name
                    );
                }
            });
        },

        selectEnvelopeView(view) {
            this.$emit('updateData', 'currentView', view);
        },
        renderFront() {
            const _this = this;
            const main = this.mainBodyFront;
            const imageAssets = this.imageAssets;

            let ctxMain = main.getContext('2d');
            let ctxIa = imageAssets.getContext('2d');

            main.width = this.envelopeClosedWidth;
            main.height = this.envelopeClosedHeight;

            // ctxMain.globalCompositeOperation = 'multiply';
            ctxMain.fillStyle = this.envelopeColor;
            ctxMain.fillRect(0, 0, main.width, main.height);

            const mainImg = new Image();
            mainImg.crossOrigin = 'anonymous';
            this.loaded.mainFrontImg = false;

            mainImg.onload = function() {
                ctxMain.drawImage(this, 0, 0, main.width, main.height);

                _this.loaded.mainFrontImg = true;
            };

            mainImg.src =
                (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_example_front_texture_transparent.png';

            // front logo image assets
            const zoom = this.canvasProps.stampZoomLevel;

            imageAssets.width = main.width;
            imageAssets.height = main.height;

            const frontLogoImg = new Image();
            frontLogoImg.crossOrigin = 'anonymous';
            this.loaded.frontLogoImg = false;

            frontLogoImg.onload = function() {
                const frontImageRatio = this.naturalWidth / this.naturalHeight;
                const boundingRatio =
                    _this.canvasProps.frontLogoMaxWidth /
                    _this.canvasProps.frontLogoMaxHeight;

                let dWidth,
                    dHeight,
                    dXOffset = 0,
                    dYOffset = 0;

                if (frontImageRatio > boundingRatio) {
                    // for longer logos
                    dWidth = _this.canvasProps.frontLogoMaxWidth;
                    dHeight =
                        _this.canvasProps.frontLogoMaxWidth / frontImageRatio;
                    dYOffset = (frontImageRatio / 200) * main.height;
                } else {
                    // for taller logos or when length === height
                    dWidth =
                        _this.canvasProps.frontLogoMaxHeight * frontImageRatio;
                    dHeight = _this.canvasProps.frontLogoMaxHeight;
                    dXOffset = main.width / 200 / frontImageRatio;
                }

                ctxIa.drawImage(
                    this,
                    (7 / 200) * main.width + dXOffset,
                    (1 / 20) * main.height + dYOffset,
                    dWidth,
                    dHeight
                );

                _this.loaded.frontLogoImg = true;
            };

            frontLogoImg.src = this.canvasProps.showFrontLogo
                ? this.canvasProps.frontLogo ||
                  (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/envelope_placeholder_logo.png'
                : '';

            const stampWidth = this.canvasProps.stampWidth;
            const stampHeight = this.canvasProps.stampHeight;
            // colored background for stamp if no stampImage selected
            if (
                !this.canvasProps.stampImage &&
                this.canvasProps.secondary_color
            ) {
                ctxIa.fillStyle =
                    this.canvasProps.secondary_color || 'transparent';
                ctxIa.fillRect(
                    (238 / 300) * main.width,
                    (27 / 300) * main.height,
                    stampWidth,
                    stampHeight
                );
            }

            // Create new image elements for stamp frame, stamp image, and postmark graphic. These will
            // be drawn sequentially with each onload handler assigning the next image's src ensuring that
            // the postmark image is drawn last and on top of the stamp
            const stampFrameImg = new Image();
            stampFrameImg.crossOrigin = 'anonymous';
            this.loaded.stampFrameImg = false;

            const stampImg = new Image();
            stampImg.crossOrigin = 'anonymous';
            this.loaded.stampImg = false;

            const postmarkImg = new Image();
            postmarkImg.crossOrigin = 'anonymous';
            this.loaded.postmarkImg = false;

            postmarkImg.onload = function() {
                ctxIa.drawImage(
                    this,
                    (191 / 300) * main.width,
                    (22 / 300) * main.height,
                    this.width,
                    this.height
                );

                _this.loaded.postmarkImg = true;
            };

            stampFrameImg.onload = function() {
                ctxIa.drawImage(
                    this,
                    (232 / 300) * main.width,
                    (17 / 300) * main.height,
                    _this.canvasProps.stampFrameWidth,
                    _this.canvasProps.stampFrameHeight
                );

                _this.loaded.stampFrameImg = true;

                stampImg.src =
                    _this.canvasProps.stampImage ||
                    (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_placeholder_stamp.png';
            };

            stampFrameImg.src =
                (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_stamp_frame.png';

            stampImg.onload = function() {
                const naturalRatio = this.naturalWidth / this.naturalHeight;
                const stampRatio = stampWidth / stampHeight;

                let constantAxis;
                let stampResizeFactor;

                if (naturalRatio < stampRatio) {
                    // for tall stamp images
                    constantAxis = 'x';
                    stampResizeFactor = (stampWidth / this.naturalWidth) * zoom;
                } else {
                    // for wide stamp images
                    constantAxis = 'y';
                    stampResizeFactor =
                        (stampHeight / this.naturalHeight) * zoom;
                }

                const sourceHeight =
                    this.naturalHeight * (naturalRatio / stampRatio);

                const sourceWidth =
                    this.naturalWidth / (naturalRatio / stampRatio);

                // the Y offset amount needed to center the image source when using drawImage
                const sourceY =
                    (stampResizeFactor * this.naturalHeight - stampHeight) /
                    stampResizeFactor /
                    2;

                // the X offset amount needed to center the image source when using drawImage
                const sourceX =
                    (stampResizeFactor * this.naturalWidth - stampWidth) /
                    stampResizeFactor /
                    2;

                // args: (image, sourceX, sourceY, sWidth, sHeight, destinationX, destinationY, dWidth, dHeight);
                ctxIa.drawImage(
                    this,
                    sourceX,
                    sourceY,
                    (constantAxis === 'y' ? sourceWidth : this.naturalWidth) /
                        zoom,
                    (constantAxis === 'x' ? sourceHeight : this.naturalHeight) /
                        zoom,
                    (317 / 400) * main.width,
                    (36 / 400) * main.height,
                    stampWidth,
                    stampHeight
                );

                _this.loaded.stampImg = true;

                if (_this.canvasProps.postmark_color !== 'none') {
                    postmarkImg.src = (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + `env_builder/img/texture-assets/envelope_postmark_frame_${_this.canvasProps.postmark_color}.png`;
                }
            };

            if (
                this.canvasProps.postmark_color !== 'none' &&
                this.canvasProps.postmark_copy.length
            ) {
                //postmark text
                ctxIa.save();
                ctxIa.rotate((15 * Math.PI) / 180);

                let canvasText = this.canvasProps.postmark_copy.split('\n');
                if (canvasText.length > 5) {
                    canvasText = canvasText.slice(0, 5);
                }

                const fontSize = 0.018 * main.width;

                for (let i = 0; i < canvasText.length; i++) {
                    ctxIa.fillStyle = this.canvasProps.postmark_color;
                    ctxIa.textAlign = 'center';
                    ctxIa.font = `bold ${fontSize}px Trebuchet MS`;
                    ctxIa.fillText(
                        canvasText[i],
                        (212 / 300) * main.width,
                        -(30 / 300) * main.height -
                            canvasText.length * ((4 / 300) * main.height) +
                            i * (9 / 300) * main.height
                    );
                }
                ctxIa.restore();
            }

            if (
                this.selectedDesign.name === 'Single Swoop' ||
                this.selectedDesign.name === 'Double Swoop'
            ) {
                // lower swoop
                ctxIa.beginPath();
                ctxIa.moveTo(0, (90 / 100) * main.height);
                ctxIa.quadraticCurveTo(
                    (7 / 12) * main.width,
                    (47 / 48) * main.height,
                    main.width,
                    (38 / 48) * main.height
                );
                ctxIa.lineTo(main.width, main.height);
                ctxIa.lineTo(0, main.height);
                ctxIa.fillStyle =
                    this.canvasProps.stripe_1_color || 'transparent';
                ctxIa.fill();
                ctxIa.closePath();
            }
            if (this.selectedDesign.name === 'Double Swoop') {
                // main/upper swoop
                ctxIa.beginPath();
                ctxIa.moveTo(0, (89 / 100) * main.height);
                ctxIa.quadraticCurveTo(
                    (7 / 12) * main.width,
                    (47 / 48) * main.height,
                    main.width,
                    (37 / 48) * main.height
                );
                ctxIa.lineTo(main.width, (21 / 25) * main.height);
                ctxIa.quadraticCurveTo(
                    (31 / 50) * main.width,
                    main.height,
                    0,
                    (45 / 48) * main.height
                );
                ctxIa.fillStyle =
                    this.canvasProps.stripe_2_color || 'transparent';
                ctxIa.fill();
                ctxIa.closePath();
            }

            if (this.selectedDesign.name === 'Air Mail Stripe') {
                const colors = ['stripe_1_color', 'stripe_2_color'];

                // used to iterate through 3 colors
                for (let i = 0; i < 8; i++) {
                    let topLeftX =
                        -(18 / 300) * main.width + i * (42 / 300) * main.width;
                    let topLeftY = (288 / 300) * main.height;

                    ctxIa.beginPath();
                    ctxIa.moveTo(topLeftX, topLeftY);
                    ctxIa.lineTo(topLeftX + (37 / 300) * main.width, topLeftY);
                    ctxIa.lineTo(
                        topLeftX + (33 / 300) * main.width,
                        main.height
                    );
                    ctxIa.lineTo(
                        topLeftX - (4 / 300) * main.width,
                        main.height
                    );

                    ctxIa.fillStyle =
                        this.canvasProps[colors[i % 2]] || '#b2b8c6';

                    ctxIa.fill();
                }
            } else if (
                this.selectedDesign.category === 'Stripes' &&
                this.selectedDesign.num_of_colors
            ) {
                ctxIa.fillStyle =
                    this.canvasProps.stripe_1_color || 'transparent';
                ctxIa.fillRect(
                    0,
                    (178 / 200) * main.height,
                    main.width,
                    (9 / 200) * main.height
                );

                if (this.selectedDesign.num_of_colors > 1) {
                    ctxIa.fillStyle =
                        this.canvasProps.stripe_2_color || 'transparent';
                    ctxIa.fillRect(
                        0,
                        (172 / 200) * main.height,
                        main.width,
                        (3 / 200) * main.height
                    );
                }

                if (this.selectedDesign.num_of_colors > 2) {
                    ctxIa.fillStyle =
                        this.canvasProps.stripe_3_color || 'transparent';
                    ctxIa.fillRect(
                        0,
                        (189 / 200) * main.height,
                        main.width,
                        (1 / 150) * main.height
                    );
                }
            }
        },

        renderBack() {
            const _this = this;
            const main = this.mainBodyBack;
            const imageAssetsBack = this.imageAssetsBack;
            const backFlapClosed = this.backFlapClosed;

            let ctxMain = main.getContext('2d');
            let ctxIab = imageAssetsBack.getContext('2d');
            let ctxBfc = backFlapClosed.getContext('2d');

            main.width = this.envelopeClosedWidth;
            main.height = this.envelopeClosedHeight;

            // ctxMain.globalCompositeOperation = 'multiply';

            /** start drawing back view rectangle */
            ctxMain.fillStyle = this.envelopeColor;
            ctxMain.fillRect(0, 0, main.width, main.height);

            const mainImg = new Image();
            mainImg.crossOrigin = 'anonymous';
            this.loaded.mainBackImg = false;

            mainImg.onload = function() {
                ctxMain.drawImage(this, 0, 0, main.width, main.height);

                _this.loaded.mainBackImg = true;
            };

            mainImg.src =
                (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_example_back_closed_texture_transparent.png';

            imageAssetsBack.width = main.width;
            imageAssetsBack.height = main.height;

            const backLogoImg = new Image();
            backLogoImg.crossOrigin = 'anonymous';
            this.loaded.backLogoImg = false;

            backLogoImg.onload = function() {
                const frontImageRatio = this.naturalWidth / this.naturalHeight;
                const boundingRatio =
                    _this.canvasProps.frontLogoMaxWidth /
                    _this.canvasProps.frontLogoMaxHeight;

                let dWidth,
                    dHeight,
                    dYOffset = 0;

                if (frontImageRatio > boundingRatio) {
                    // for longer logos
                    dWidth = _this.canvasProps.frontLogoMaxWidth;
                    dHeight =
                        _this.canvasProps.frontLogoMaxWidth / frontImageRatio;
                    dYOffset = -(4 / 5) * dHeight;
                } else {
                    // for taller logos or when length === height
                    dWidth =
                        _this.canvasProps.frontLogoMaxHeight * frontImageRatio;
                    dHeight = _this.canvasProps.frontLogoMaxHeight;
                    dYOffset = -(3 / 4) * dHeight;
                }

                ctxIab.drawImage(
                    this,
                    (main.width - dWidth) / 2,
                    (1 / 2) * topFlapHeight + dYOffset,
                    dWidth,
                    dHeight
                );
                _this.loaded.backLogoImg = true;
            };

            backLogoImg.src = this.canvasProps.showBackLogo
                ? this.canvasProps.backLogo
                : '';

            /** end drawing back view rectangle part */

            /** start drawing back view's triangle flap in closed position */
            const topFlapHeight = this.topFlapHeight;

            backFlapClosed.width = main.width;
            backFlapClosed.height = topFlapHeight;

            // ctxBfc.globalCompositeOperation = 'multiply';
            ctxBfc.beginPath();
            ctxBfc.moveTo(0, 0);
            ctxBfc.quadraticCurveTo(
                (1 / 100) * main.width,
                (19 / 100) * topFlapHeight,
                (15 / 100) * main.width,
                (41 / 100) * topFlapHeight
            );
            ctxBfc.lineTo((83 / 200) * main.width, (87 / 100) * topFlapHeight);
            // drawing the top tip of triangle for closed flap
            ctxBfc.quadraticCurveTo(
                (1 / 2) * main.width,
                (105 / 100) * topFlapHeight,
                (117 / 200) * main.width,
                (87 / 100) * topFlapHeight
            );
            ctxBfc.lineTo((85 / 100) * main.width, (41 / 100) * topFlapHeight);
            ctxBfc.quadraticCurveTo(
                (99 / 100) * main.width,
                (19 / 100) * topFlapHeight,
                main.width,
                0
            );
            ctxBfc.lineTo(main.width, 0);
            ctxBfc.clip();
            ctxBfc.fillStyle = this.envelopeColor;
            ctxBfc.fillRect(0, 0, main.width, topFlapHeight);
            ctxBfc.closePath();

            // load texture
            const flapTextureImg = new Image();
            flapTextureImg.crossOrigin = 'anonymous';
            this.loaded.flapTextureImg = false;

            flapTextureImg.onload = function() {
                ctxBfc.drawImage(this, 0, 0, main.width, main.height);
                _this.loaded.flapTextureImg = true;
            };

            flapTextureImg.src =
                (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_example_front_texture_transparent.png';
            /** end drawing back view's triangle flap in closed position */
        },

        renderBackFlap() {
            const _this = this;
            const main = this.mainBodyBackFlap;
            const backFlapOpened = this.backFlapOpened;
            const linerUpper = this.linerUpper;
            const linerLower = this.linerLower;

            let ctxMain = main.getContext('2d');
            let ctxBfo = backFlapOpened.getContext('2d');
            let ctxLu = linerUpper.getContext('2d');
            let ctxLl = linerLower.getContext('2d');

            const topFlapHeight = this.topFlapHeight;

            main.width = this.envelopeClosedWidth;
            main.height = this.envelopeClosedHeight + topFlapHeight;
            let mainRectHeight = this.envelopeClosedHeight;

            /** start drawing back flap view's lower flaps (the three flaps that sit in front of the envelope liner) */
            // ctxMain.globalCompositeOperation = 'multiply';
            ctxMain.beginPath();
            ctxMain.moveTo(0, topFlapHeight);

            ctxMain.lineTo((5 / 200) * main.width, topFlapHeight);
            ctxMain.quadraticCurveTo(
                (9 / 200) * main.width,
                topFlapHeight,
                (17 / 200) * main.width,
                topFlapHeight + (6 / 200) * mainRectHeight
            );
            ctxMain.quadraticCurveTo(
                (95 / 200) * main.width,
                topFlapHeight + (88 / 200) * mainRectHeight,
                (95 / 200) * main.width,
                topFlapHeight + (93 / 200) * mainRectHeight
            );
            // widow's peak of middle opening that shows the lower liner behind
            ctxMain.quadraticCurveTo(
                (50 / 100) * main.width,
                topFlapHeight + (98 / 200) * mainRectHeight,
                (105 / 200) * main.width,
                topFlapHeight + (93 / 200) * mainRectHeight
            );

            ctxMain.quadraticCurveTo(
                (105 / 200) * main.width,
                topFlapHeight + (88 / 200) * mainRectHeight,
                (183 / 200) * main.width,
                topFlapHeight + (6 / 200) * mainRectHeight
            );
            ctxMain.quadraticCurveTo(
                (191 / 200) * main.width,
                topFlapHeight,
                (195 / 200) * main.width,
                topFlapHeight
            );
            ctxMain.lineTo(main.width, topFlapHeight);
            ctxMain.lineTo(main.width, main.height);
            ctxMain.lineTo(0, main.height);
            ctxMain.clip();
            // done drawing clipping path. behind this shape will sit the lower part of the envelope liner

            // applying envelope_color to rectangle shape that clipping path above will mask
            ctxMain.fillStyle = this.envelopeColor;
            ctxMain.fillRect(0, topFlapHeight, main.width, main.height);

            const mainImg = new Image();
            mainImg.crossOrigin = 'anonymous';
            this.loaded.mainBackFlapImg = false;

            mainImg.onload = function() {
                ctxMain.drawImage(
                    this,
                    0,
                    topFlapHeight,
                    main.width,
                    mainRectHeight
                );

                _this.loaded.mainBackFlapImg = true;
            };

            mainImg.src =
                (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_example_back_opened_texture_transparent.png';
            /** end drawing back flap view's lower flaps (the three flaps that sit in front of the envelope liner) */

            /** start drawing back flap view's triangle flap outer part in opened position (main envelope_color part; not the liner) */
            backFlapOpened.width = main.width;
            backFlapOpened.height = topFlapHeight;

            // ctxBfo.globalCompositeOperation = 'multiply';
            ctxBfo.beginPath();
            ctxBfo.moveTo(0, topFlapHeight);
            ctxBfo.quadraticCurveTo(
                (1 / 100) * main.width,
                (81 / 100) * topFlapHeight,
                (15 / 100) * main.width,
                (59 / 100) * topFlapHeight
            );
            ctxBfo.lineTo((83 / 200) * main.width, (13 / 100) * topFlapHeight);
            // drawing the top tip of triangle for open flap
            ctxBfo.quadraticCurveTo(
                (1 / 2) * main.width,
                -((5 / 100) * topFlapHeight),
                (117 / 200) * main.width,
                (13 / 100) * topFlapHeight
            );
            ctxBfo.lineTo((85 / 100) * main.width, (59 / 100) * topFlapHeight);
            ctxBfo.quadraticCurveTo(
                (99 / 100) * main.width,
                (81 / 100) * topFlapHeight,
                main.width,
                topFlapHeight
            );
            ctxBfo.lineTo(main.width, topFlapHeight);
            const grdBackFlapOpenedShade = ctxBfo.createLinearGradient(
                0,
                0,
                0,
                topFlapHeight
            );
            grdBackFlapOpenedShade.addColorStop(0, 'rgba(0,0,0,0.2)');
            grdBackFlapOpenedShade.addColorStop(1, 'transparent');
            ctxBfo.fillStyle = grdBackFlapOpenedShade;
            ctxBfo.fill();
            ctxBfo.clip();
            ctxBfo.fillStyle = this.envelopeColor;
            ctxBfo.fillRect(0, 0, main.width, topFlapHeight);
            ctxBfo.closePath();

            // load texture
            const flapTextureImg = new Image();
            flapTextureImg.crossOrigin = 'anonymous';
            this.loaded.flapTextureImg = false;

            flapTextureImg.onload = function() {
                ctxBfo.drawImage(this, 0, 0, main.width, main.height);
                _this.loaded.flapTextureImg = true;
            };

            flapTextureImg.src =
                (window.config.thankviewAssetsPath || 'https://assets.thankview.com/assets') + 'env_builder/img/texture-assets/envelope_example_front_texture_transparent.png';
            /** end drawing back flap view's triangle flap outer part in opened position (main envelope_color part; not the liner) */

            /** start drawing back flap view's triangle flap liner (linerUpper) in opened position */
            linerUpper.width = main.width;
            linerUpper.height = topFlapHeight + 1;

            const grdLinerUpper = ctxMain.createLinearGradient(
                0,
                0,
                0,
                topFlapHeight
            );
            grdLinerUpper.addColorStop(0, 'transparent');
            grdLinerUpper.addColorStop(1, 'rgba(0,0,0,0.3)');

            // ctxLu.globalCompositeOperation = 'multiply';
            ctxLu.beginPath();
            ctxLu.moveTo((9 / 200) * main.width, topFlapHeight + 1);
            ctxLu.quadraticCurveTo(
                (7 / 100) * main.width,
                (87 / 100) * topFlapHeight,
                (13 / 100) * main.width,
                (154 / 200) * topFlapHeight
            );
            ctxLu.lineTo((184 / 400) * main.width, (81 / 400) * topFlapHeight);
            // tip of liner upper triangle
            ctxLu.quadraticCurveTo(
                (1 / 2) * main.width,
                (61 / 400) * topFlapHeight,
                (216 / 400) * main.width,
                (81 / 400) * topFlapHeight
            );
            ctxLu.lineTo((87 / 100) * main.width, (154 / 200) * topFlapHeight);
            ctxLu.quadraticCurveTo(
                (93 / 100) * main.width,
                (87 / 100) * topFlapHeight,
                (191 / 200) * main.width,
                topFlapHeight + 1
            );

            ctxLu.fillStyle = this.linerColor;
            ctxLu.fill();
            ctxLu.clip();
            ctxLu.fillStyle = grdLinerUpper;
            ctxLu.fillRect(0, 0, main.width, topFlapHeight);
            /** end drawing back flap view's triangle flap liner (linerUpper) in opened position */

            /** start drawing back flap view's envelope liner rectangle (linerLower). this is also used later
             * for the backImg render (the liner rectangle shape that sits begind the flapsImg on landing page) */
            linerLower.width = main.width;
            linerLower.height = main.height;
            // ctxLl.globalCompositeOperation = 'multiply';

            const grdLinerLower = ctxLl.createLinearGradient(
                0,
                topFlapHeight,
                0,
                main.height
            );
            grdLinerLower.addColorStop(0, 'transparent');
            grdLinerLower.addColorStop(1, 'rgba(0,0,0,0.3)');

            // main rectangle using liner_color
            ctxLl.fillStyle = this.linerColor;
            ctxLl.fillRect(
                0,
                topFlapHeight,
                main.width,
                this.envelopeClosedHeight
            );
            // multiply gradient rectangle on top
            ctxLl.fillStyle = grdLinerLower;
            ctxLl.fillRect(
                0,
                topFlapHeight,
                main.width,
                this.envelopeClosedHeight
            );

            /** end drawing back flap view's envelope liner rectangle (linerLower) */
        },

        renderImageExports() {
            // final composite
            const finalCanvas = document.getElementById('final-canvas');
            const mainBodyFront = this.mainBodyFront;
            const imageAssets = this.imageAssets;
            const imageAssetsBack = this.imageAssetsBack;
            const mainBodyBack = this.mainBodyBack;
            const mainBodyBackFlap = this.mainBodyBackFlap;
            const backFlapClosed = this.backFlapClosed;
            const backFlapOpened = this.backFlapOpened;
            const linerUpper = this.linerUpper;
            const linerLower = this.linerLower;

            const ctxFf = finalCanvas.getContext('2d');

            // 1-A/9: frontImg
            ctxFf.save();
            finalCanvas.width = mainBodyFront.width;
            finalCanvas.height = mainBodyFront.height;

            ctxFf.drawImage(mainBodyFront, 0, 0);
            ctxFf.drawImage(imageAssets, 0, 0);

            this.$emit(
                'updateImageExport',
                'frontImg',
                finalCanvas.toDataURL('image/jpeg', 0.99)
            );

            ctxFf.fillStyle = this.canvasProps.envelope_text_color;
            ctxFf.textAlign = 'center';
            ctxFf.font = `normal 48px Sans-serif`;
            ctxFf.fillText(
                'Recipient Name',
                (1 / 2) * finalCanvas.width,
                (24 / 50) * finalCanvas.height
            );

            this.$emit(
                'updateImageExport',
                'frontWithTextPreview',
                finalCanvas.toDataURL('image/jpeg', 0.99)
            );

            // 1-B/9 frontSmallImg
            ctxFf.restore();
            finalCanvas.width = 550;
            finalCanvas.height = 360;

            ctxFf.drawImage(
                mainBodyFront,
                0,
                0,
                finalCanvas.width,
                finalCanvas.height
            );
            ctxFf.drawImage(
                imageAssets,
                0,
                0,
                finalCanvas.width,
                finalCanvas.height
            );

            this.$emit(
                'updateImageExport',
                'frontSmallImg',
                finalCanvas.toDataURL('image/jpeg', 0.99)
            );

            // 2/9: backImg
            ctxFf.restore();
            finalCanvas.width = mainBodyBack.width;
            finalCanvas.height = mainBodyBack.height;

            ctxFf.drawImage(mainBodyBack, 0, 0);
            ctxFf.drawImage(imageAssetsBack, 0, 0);

            this.$emit(
                'updateImageExport',
                'backImg',
                finalCanvas.toDataURL('image/png')
            );

            // 3/9: backFlapImg
            ctxFf.restore();
            finalCanvas.width = mainBodyBackFlap.width;
            finalCanvas.height = mainBodyBackFlap.height;

            ctxFf.drawImage(backFlapOpened, 0, 0);
            ctxFf.drawImage(linerUpper, 0, 0);
            ctxFf.drawImage(linerLower, 0, 0);
            ctxFf.drawImage(mainBodyBackFlap, 0, 0);

            this.$emit(
                'updateImageExport',
                'backFlapImg',
                finalCanvas.toDataURL('image/png')
            );

            // 4/9: flapTopOpenImg
            ctxFf.restore();
            finalCanvas.width = this.envelopeClosedWidth;
            finalCanvas.height = this.topFlapHeight;

            ctxFf.drawImage(backFlapOpened, 0, 0);
            ctxFf.drawImage(linerUpper, 0, 0);

            this.$emit(
                'updateImageExport',
                'flapTopOpenImg',
                finalCanvas.toDataURL('image/png')
            );

            // 5/9: flapsBottomImg
            ctxFf.restore();
            finalCanvas.width = this.envelopeClosedWidth;
            finalCanvas.height = this.envelopeClosedHeight;

            ctxFf.drawImage(
                mainBodyBackFlap,
                0,
                backFlapOpened.height,
                mainBodyBackFlap.width,
                mainBodyBackFlap.height - backFlapOpened.height,
                0,
                0,
                finalCanvas.width,
                finalCanvas.height
            );

            this.$emit(
                'updateImageExport',
                'flapsBottomImg',
                finalCanvas.toDataURL('image/png')
            );

            // 6/9: flapTopCloseImg
            ctxFf.restore();
            const resizeFactorBackFlapClosed =
                this.envelopeClosedWidth / backFlapClosed.width;
            finalCanvas.width = this.envelopeClosedWidth;
            finalCanvas.height = this.topFlapHeight;

            ctxFf.shadowColor = 'rgba(0, 0, 0, 0.4)';
            ctxFf.shadowOffsetY = 4;
            ctxFf.shadowBlur = 12;
            ctxFf.drawImage(backFlapClosed, 0, 0);
            ctxFf.shadowColor = 'transparent';
            ctxFf.drawImage(
                imageAssetsBack,
                0,
                0,
                finalCanvas.width,
                resizeFactorBackFlapClosed * imageAssetsBack.height
            );

            this.$emit(
                'updateImageExport',
                'flapTopCloseImg',
                finalCanvas.toDataURL('image/png')
            );

            // 7/9: linerLowerImg
            ctxFf.restore();

            finalCanvas.width = this.envelopeClosedWidth;
            finalCanvas.height = this.envelopeClosedHeight;

            ctxFf.drawImage(
                this.linerLower,
                0,
                this.topFlapHeight,
                this.linerLower.width,
                this.linerLower.height - this.topFlapHeight,
                0,
                0,
                finalCanvas.width,
                finalCanvas.height
            );

            this.$emit(
                'updateImageExport',
                'linerLowerImg',
                finalCanvas.toDataURL('image/jpeg', 0.99)
            );

            // 8/9: shareImg
            ctxFf.restore();
            finalCanvas.width = 1727;
            finalCanvas.height = 907;

            ctxFf.drawImage(
                mainBodyFront,
                (finalCanvas.width - mainBodyFront.width) / 2,
                (1 / 12) * mainBodyFront.height,
                mainBodyFront.width,
                mainBodyFront.height
            );

            ctxFf.drawImage(
                imageAssets,
                (finalCanvas.width - imageAssets.width) / 2,
                (1 / 12) * imageAssets.height,
                imageAssets.width,
                imageAssets.height
            );

            // 'Thank You' text in shareImg
            ctxFf.fillStyle = this.canvasProps.envelope_text_color;
            ctxFf.textAlign = 'center';
            ctxFf.font = `normal 48px Sans-serif`;
            ctxFf.fillText(
                'Thank You',
                (1 / 2) * finalCanvas.width,
                (24 / 50) * finalCanvas.height
            );

            // draw gradient dropshadow in shareImg
            ctxFf.beginPath();
            ctxFf.moveTo(
                (finalCanvas.width - mainBodyFront.width) / 2,
                (13 / 12) * mainBodyFront.height
            );
            ctxFf.lineTo(
                finalCanvas.width -
                    (finalCanvas.width - mainBodyFront.width) / 2,
                (13 / 12) * mainBodyFront.height
            );
            ctxFf.lineTo((6 / 7) * finalCanvas.width, finalCanvas.height);
            ctxFf.lineTo((1 / 7) * finalCanvas.width, finalCanvas.height);
            const grdShareShadow = ctxFf.createLinearGradient(
                0,
                mainBodyFront.height,
                0,
                (49 / 50) * finalCanvas.height
            );
            grdShareShadow.addColorStop(0, 'rgba(0,0,0,0.2)');
            grdShareShadow.addColorStop(1, 'transparent');
            ctxFf.fillStyle = grdShareShadow;
            ctxFf.fill();

            this.$emit(
                'updateImageExport',
                'shareImg',
                finalCanvas.toDataURL('image/png')
            );

            // 9/9: iconImg
            ctxFf.restore();
            finalCanvas.width = 600;
            finalCanvas.height = 637;

            ctxFf.fillStyle = 'white';
            ctxFf.fillRect(0, 0, finalCanvas.width, finalCanvas.height);
            const resizeFactorBackFlap =
                ((45 / 50) * finalCanvas.width) / mainBodyBackFlap.width;
            const resizeFactorFront =
                ((18 / 20) * finalCanvas.width) / mainBodyFront.width;

            ctxFf.drawImage(
                backFlapOpened,
                finalCanvas.width - resizeFactorBackFlap * backFlapOpened.width,
                0,
                resizeFactorBackFlap * backFlapOpened.width,
                resizeFactorBackFlap * backFlapOpened.height
            );
            ctxFf.drawImage(
                linerUpper,
                finalCanvas.width - resizeFactorBackFlap * linerUpper.width,
                0,
                resizeFactorBackFlap * linerUpper.width,
                resizeFactorBackFlap * linerUpper.height
            );
            ctxFf.drawImage(
                linerLower,
                finalCanvas.width - resizeFactorBackFlap * linerLower.width,
                0,
                resizeFactorBackFlap * linerLower.width,
                resizeFactorBackFlap * linerLower.height
            );
            ctxFf.drawImage(
                mainBodyBackFlap,
                finalCanvas.width -
                    resizeFactorBackFlap * mainBodyBackFlap.width,
                0,
                resizeFactorBackFlap * mainBodyBackFlap.width,
                resizeFactorBackFlap * mainBodyBackFlap.height
            );

            ctxFf.shadowColor = 'black';
            ctxFf.shadowBlur = 15;
            ctxFf.save();

            // make a clpping path and then render shadow but only have
            // shadow cast on the open back flap drawing in background
            ctxFf.beginPath();
            ctxFf.moveTo(
                finalCanvas.width -
                    resizeFactorBackFlap * mainBodyBackFlap.width,
                0
            );
            ctxFf.lineTo(finalCanvas.width, 0);
            ctxFf.lineTo(
                finalCanvas.width,
                resizeFactorBackFlap * mainBodyBackFlap.height
            );
            ctxFf.lineTo(
                finalCanvas.width -
                    resizeFactorBackFlap * mainBodyBackFlap.width,
                resizeFactorBackFlap * mainBodyBackFlap.height
            );
            ctxFf.clip();

            ctxFf.drawImage(
                mainBodyFront,
                0,
                finalCanvas.height - resizeFactorFront * mainBodyFront.height,
                resizeFactorFront * mainBodyFront.width,
                resizeFactorFront * mainBodyFront.height
            );
            ctxFf.restore();

            // no shadow for final foreground draw of envelope front
            ctxFf.shadowBlur = 0;
            ctxFf.drawImage(
                mainBodyFront,
                0,
                finalCanvas.height - resizeFactorFront * mainBodyFront.height,
                resizeFactorFront * mainBodyFront.width,
                resizeFactorFront * mainBodyFront.height
            );
            ctxFf.drawImage(
                imageAssets,
                0,
                finalCanvas.height - resizeFactorFront * imageAssets.height,
                resizeFactorFront * imageAssets.width,
                resizeFactorFront * imageAssets.height
            );

            this.$emit(
                'updateImageExport',
                'iconImg',
                finalCanvas.toDataURL('image/jpeg', 0.99)
            );
            this.$emit('updateData', 'processingImages', false);
        },

        changeStep(stepNumber) {
            this.$emit('changeStep', stepNumber);
        },

        saveEnvelope() {
            this.$emit('saveEnvelope');
        }
    },
};
</script>