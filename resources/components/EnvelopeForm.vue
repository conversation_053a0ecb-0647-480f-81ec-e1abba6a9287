<template>
    <div
        ref="options"
        class="options pad-r-40px pad-l-40px overflow-x-hidden overflow-y-auto"
    >
        <form
            ref="envOptions"
            name="envOptions"
            class="collapsible-form pad-b-32px"
        >
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Envelope Title
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <label
                    for="envelope-title"
                    class="block width-100 font-charcoal font-w-7 margin-b-8px required"
                >
                    Give your envelope a title
                </label>
                <div class="build-form__input-wrapper">
                    <input
                        id="envelope-title"
                        v-model="canvasProps.name"
                        type="text"
                        placeholder="Untitled Envelope"
                    >
                </div>
                <div
                    v-if="submissionError && !valid.canvasProps.name.required"
                    class="font-red-45 margin-t-4px"
                >
                    Give your envelope a title.
                </div>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Colors
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    Add brand colors from your branding guidelines to your
                    envelope design.
                    <help-tip>
                        <strong>Helpful Tip:</strong>
                        If your organization has a marketing department, you can
                        request branding guidelines from them. Visit our
                        <a
                            href="https://community.thankview.com/t/q6hla9a#making-your-branding-visually-accessible"
                            target="_blank"
                        >FAQ</a>
                        for more info on visual branding and accessibility.
                    </help-tip>
                </p>

                <hex-color-input
                    v-model="canvasProps.primary_color"
                    class="margin-t-32px required"
                    :name="'primary_color'"
                    @input="setDisplayColor"
                >
                    Primary Brand Color
                </hex-color-input>
                <div
                    v-if="
                        !valid.canvasProps.primary_color.isHex ||
                            (submissionError &&
                                !valid.canvasProps.primary_color.required)
                    "
                    class="font-red-45 margin-t-4px"
                >
                    Please enter a valid hex code.
                </div>

                <hex-color-input
                    v-model="canvasProps.secondary_color"
                    class="margin-t-32px required"
                    :name="'secondary_color'"
                    @input="setDisplayColor"
                >
                    Secondary Brand Color
                </hex-color-input>
                <div
                    v-if="
                        !valid.canvasProps.secondary_color.isHex ||
                            (submissionError &&
                                !valid.canvasProps.secondary_color.required)
                    "
                    class="font-red-45 margin-t-4px"
                >
                    Please enter a valid hex code.
                </div>

                <hex-color-input
                    v-model="canvasProps.tertiary_color"
                    class="margin-t-32px"
                    :name="'tertiary_color'"
                    @input="setDisplayColor"
                >
                    Tertiary Brand Color
                </hex-color-input>
                <div
                    v-if="!valid.canvasProps.tertiary_color.isHex"
                    class="font-red-45 margin-t-4px"
                >
                    Please enter a valid hex code.
                </div>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Envelope Color
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    <a
                        href="https://community.thankview.com/t/y4hzs17#examples-of-great-envelope-designs"
                        target="_blank"
                    >Click here</a>
                    for examples.
                </p>
                <hex-color-input
                    v-model="canvasProps.envelope_color"
                    class="margin-t-32px required"
                    :name="'envelope_color'"
                    @input="setDisplayColor"
                >
                    Select a Color
                </hex-color-input>
                <div
                    v-if="
                        !valid.canvasProps.envelope_color.isHex ||
                            (submissionError &&
                                !valid.canvasProps.envelope_color.required)
                    "
                    class="font-red-45 margin-t-4px"
                >
                    Please enter a valid hex code.
                </div>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Recipient Name Color
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    The recipient name color should complement and contrast the
                    envelope color.
                    <help-tip>
                        <strong>Helpful Tip:</strong> White (#FFFFFF), Black
                        (#000000), or a Dark Grey (#A9A9A9) are usually suitable
                        colors for the recipient name color.
                    </help-tip>
                </p>
                <hex-color-input
                    v-model="canvasProps.envelope_text_color"
                    class="margin-t-32px required"
                    :name="'envelope_text_color'"
                    @input="setDisplayColor"
                >
                    Select a Color
                </hex-color-input>
                <div
                    v-if="
                        !valid.canvasProps.envelope_text_color.isHex ||
                            (submissionError &&
                                !valid.canvasProps.envelope_text_color.required)
                    "
                    class="font-red-45 margin-t-4px"
                >
                    Please enter a valid hex code.
                </div>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Logo
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    Add a logo in .jpeg or png format to establish familiarity
                    and professional attention with your recipients.
                    <help-tip>
                        <strong>Helpful Tip:</strong> Logos in the .PNG format
                        works best because they can be saved without a
                        background.
                    </help-tip>
                </p>
                <div class="flex">
                    <div class="margin-r-24px">
                        <input
                            id="show-front-logo"
                            v-model="canvasProps.showFrontLogo"
                            class="tv-radio-button"
                            type="radio"
                            :value="true"
                        >
                        <label for="show-front-logo">Logo</label>
                    </div>
                    <div>
                        <input
                            id="hide-front-logo"
                            v-model="canvasProps.showFrontLogo"
                            class="tv-radio-button"
                            type="radio"
                            :value="false"
                        >
                        <label for="hide-front-logo">No Logo</label>
                    </div>
                </div>
                <file-uploader
                    v-if="canvasProps.showFrontLogo"
                    :file-upload-id="'frontLogo'"
                    :new-file-data="canvasProps.frontLogo"
                    @setImage="setImage"
                />
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Postmark
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    A detailed touch that adds to the effect of the envelope.
                    Max: 40 chars, 4 line-breaks.
                    <help-tip>
                        <strong>Helpful Tip:</strong> Keep the copy short by
                        using professional acronyms.
                    </help-tip>
                </p>
                <div class="margin-r-24px">
                    <input
                        id="postmark-black"
                        v-model="canvasProps.postmark_color"
                        class="tv-radio-button"
                        type="radio"
                        value="black"
                    >
                    <label for="postmark-black">Black Postmark</label>
                </div>
                <div>
                    <input
                        id="postmark-white"
                        v-model="canvasProps.postmark_color"
                        class="tv-radio-button"
                        type="radio"
                        value="white"
                    >
                    <label for="postmark-white">White Postmark</label>
                </div>
                <div>
                    <input
                        id="postmark-none"
                        v-model="canvasProps.postmark_color"
                        class="tv-radio-button"
                        type="radio"
                        value="none"
                    >
                    <label for="postmark-none">No postmark</label>
                </div>

                <label
                    for="postmark-copy-id"
                    class="
                        block
                        font-charcoal font-w-7
                        margin-t-32px margin-b-8px
                    "
                >Postmark Inner Copy</label>
                <textarea
                    id="postmark-copy-id"
                    v-model="canvasProps.postmark_copy"
                    class="input-standard"
                    maxlength="40"
                />
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Stamp
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    Include a stamp in .jpeg or .png format that can add
                    personality or emphasize the message you want to share.
                    <help-tip>
                        <strong>Helpful Tip:</strong> Upload a high-quality
                        image in the form of a .jpeg or .png for your custom
                        stamp.
                    </help-tip>
                </p>
                <div
                    v-if="canvasProps.stampImage"
                    class="stamp-preview relative"
                    :style="stampImageStyle"
                >
                    <div class="crop-frame" />
                    <div
                        class="
                            image-edit-border
                            fs-ignore-rage-clicks
                            pad-t-8px pad-r-8px
                        "
                    >
                        <button
                            type="button"
                            class="image-remove absolute"
                            @click="resetStamp"
                        >
                            <i class="fa fa-times-circle" />
                        </button>
                        <button
                            type="button"
                            class="image-zoom-in absolute"
                            @click="$emit('zoomImage', true)"
                        >
                            <i class="fa fa-plus-circle" />
                        </button>
                        <button
                            type="button"
                            class="image-zoom-out absolute"
                            @click="$emit('zoomImage', false)"
                        >
                            <i class="fa fa-minus-circle" />
                        </button>
                    </div>
                </div>
                <file-uploader
                    :file-upload-id="'stampImage'"
                    :new-file-data="canvasProps.stampImage"
                    :hide-preview="true"
                    @setImage="setImage"
                />
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Front Design
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    A simple design to elevate the look of your envelope.
                    <help-tip>
                        <strong>Helpful Tip:</strong> Discover your
                        organization’s branding guidelines to help you select
                        complementary colors.
                    </help-tip>
                </p>

                <input
                    id="front_design-no"
                    v-model="canvasProps.design_id"
                    class="tv-radio-button"
                    type="radio"
                    :value="null"
                >
                <label for="front_design-no">No Design</label>

                <h3 class="margin-b-4px">
                    Swoops
                </h3>
                <div class="flex">
                    <div
                        v-for="design in swoopDesigns"
                        :key="design.name"
                        class="margin-r-16px"
                    >
                        <input
                            :id="`design-${design.name
                                .toLowerCase()
                                .replace(' ', '-')}`"
                            v-model="canvasProps.design_id"
                            class="tv-radio-button"
                            type="radio"
                            :value="design.id"
                        >
                        <label
                            :for="`design-${design.name
                                .toLowerCase()
                                .replace(' ', '-')}`"
                        >{{ design.name }}</label>
                    </div>
                </div>
                <h3 class="flex-50">
                    Stripes
                </h3>
                <div class="flex flex-wrap">
                    <div
                        v-for="design in stripeDesigns"
                        :key="design.name"
                        class="margin-r-16px"
                    >
                        <input
                            :id="`design-${design.name
                                .toLowerCase()
                                .replace(' ', '-')}`"
                            v-model="canvasProps.design_id"
                            class="tv-radio-button"
                            type="radio"
                            :value="design.id"
                        >
                        <label
                            :for="`design-${design.name
                                .toLowerCase()
                                .replace(' ', '-')}`"
                        >{{ design.name }}</label>
                    </div>
                </div>
                <hex-color-input
                    v-if="selectedDesign.num_of_colors"
                    v-model="canvasProps.stripe_1_color"
                    class="margin-t-32px"
                    :name="'stripe_1_color'"
                    @input="setDisplayColor"
                >
                    {{
                        selectedDesign.category === 'Stripes'
                            ? 'Stripe'
                            : 'Swoop'
                    }}
                    1 Color
                    <div
                        v-if="!valid.canvasProps.stripe_1_color.isHex || !valid.canvasProps.stripe_1_color.required"
                        slot="error-message"
                        class="font-red-45 margin-t-4px"
                    >
                        Please enter a color in the format #F0F0F0
                    </div>
                </hex-color-input>
                <hex-color-input
                    v-if="selectedDesign.num_of_colors > 1"
                    v-model="canvasProps.stripe_2_color"
                    class="margin-t-32px"
                    :name="'stripe_2_color'"
                    @input="setDisplayColor"
                >
                    {{
                        selectedDesign.category === 'Stripes'
                            ? 'Stripe'
                            : 'Swoop'
                    }}
                    2 Color
                    <div
                        v-if="!valid.canvasProps.stripe_2_color.isHex || !valid.canvasProps.stripe_2_color.required"
                        slot="error-message"
                        class="font-red-45 margin-t-4px"
                    >
                        Please enter a color in the format #F0F0F0
                    </div>
                </hex-color-input>
                <hex-color-input
                    v-if="selectedDesign.num_of_colors > 2"
                    v-model="canvasProps.stripe_3_color"
                    class="margin-t-32px"
                    :name="'stripe_3_color'"
                    @input="setDisplayColor"
                >
                    {{
                        selectedDesign.category === 'Stripes'
                            ? 'Stripe'
                            : 'Swoop'
                    }}
                    3 Color
                    <div
                        v-if="!valid.canvasProps.stripe_3_color.isHex || !valid.canvasProps.stripe_3_color.required"
                        slot="error-message"
                        class="font-red-45 margin-t-4px"
                    >
                        Please enter a color in the format #F0F0F0
                    </div>
                </hex-color-input>
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Back Flap Logo
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    Adding a logo to the back flap of the envelope can help
                    reemphasize branding.
                    <help-tip>
                        <strong>Helpful Tip:</strong> Upload a high-quality logo
                        in the form of a .jpeg or .png for your back flap.
                    </help-tip>
                </p>
                <div class="flex">
                    <div class="margin-r-24px">
                        <input
                            id="show-back-logo"
                            v-model="canvasProps.showBackLogo"
                            class="tv-radio-button"
                            type="radio"
                            :value="true"
                        >
                        <label for="show-back-logo">Logo</label>
                    </div>
                    <div>
                        <input
                            id="hide-back-logo"
                            v-model="canvasProps.showBackLogo"
                            class="tv-radio-button"
                            type="radio"
                            :value="false"
                        >
                        <label for="hide-back-logo">No Logo</label>
                    </div>
                </div>

                <file-uploader
                    v-if="canvasProps.showBackLogo"
                    :file-upload-id="'backLogo'"
                    :new-file-data="canvasProps.backLogo"
                    @setImage="setImage"
                />
            </div>
            <div class="open pad-b-32px">
                <h2
                    class="
                        cursor-pointer
                        font-charcoal font-size-20px font-w-5
                        pad-t-16px pad-b-16px
                        margin-t-0px margin-b-0px
                    "
                    @click="toggleSection"
                >
                    Envelope Liner Color
                    <i
                        class="
                            fas
                            fa-chevron-down
                            float-right
                            pointer-events-none
                        "
                    />
                </h2>
                <p class="relative margin-t-0px">
                    The envelope liner color should complement your primary
                    branding color.
                    <help-tip>
                        <strong>Helpful Tip:</strong> Sometimes a neutral color
                        like white (#FFFFF) may work in place of a branding
                        color. Discover your organization’s branding guidelines
                        to help you select a complementary color.
                    </help-tip>
                </p>
                <hex-color-input
                    v-model="canvasProps.liner_color"
                    class="margin-t-32px required"
                    :name="'liner_color'"
                    @input="setDisplayColor"
                >
                    Select a Color
                </hex-color-input>
                <div
                    v-if="
                        !valid.canvasProps.liner_color.isHex ||
                            (submissionError &&
                                !valid.canvasProps.liner_color.required)
                    "
                    class="font-red-45 margin-t-4px"
                >
                    Please enter a valid hex code.
                </div>
            </div>
        </form>
    </div>
</template>

<script>
import HelpTip from './HelpTip';
import FileUploader from './FileUploader';
import HexColorInput from './HexColorInput';

export default {
    components: {
        'file-uploader': FileUploader,
        'hex-color-input': HexColorInput,
        'help-tip': HelpTip,
    },
    props: {
        canvasProps: Object,
        valid: Object,
        submissionError: Boolean,
        selectedDesign: Object,
        designOptions: Array,
    },
    // populate the form with defaults
    data() {
        return {
            stampImageNaturalRatio: 0,
        };
    },

    computed: {
        stripeDesigns() {
            return this.designOptions.filter(d => d.category === 'Stripes');
        },

        swoopDesigns() {
            return this.designOptions.filter(d => d.category === 'Swoops');
        },

        designType() {
            return this.selectedDesign.category
                ? this.selectedDesign.category.slice(0, -1)
                : '';
        },

        stampRatio() {
            return this.canvasProps.stampWidth / this.canvasProps.stampHeight;
        },

        stampImageStyle() {
            let styleObj = {
                backgroundImage: `url(${this.canvasProps.stampImage})`,
                backgroundRepeat: 'no-repeat',
                backgroundPosition: 'center',
            };

            const stampDisplayWidth = 236;
            const stampDisplayHeight = 150;

            if (this.stampImageNaturalRatio < this.stampRatio) {
                styleObj.backgroundSize = `${this.canvasProps.stampZoomLevel *
                    stampDisplayWidth}px auto`;
            } else {
                styleObj.backgroundSize = `auto ${this.canvasProps
                    .stampZoomLevel * stampDisplayHeight}px`;
            }
            return styleObj;
        },
    },

    watch: {
        submissionError(newVal) {
            // scroll to first input error
            if (newVal === true) {
                setTimeout(() => {
                    const inputErrors = document.getElementsByClassName(
                        'font-red-45'
                    );
                    if (inputErrors.length) {
                        const firstInputError = inputErrors[0];
                        this.$refs.options.scrollTop = firstInputError.offsetTop - 350;
                    }
                }, 10);
            }
        },
    },

    methods: {
        setDisplayColor($event, propKey) {
            let val = $event.target.value;
            // inject # if it's not entered
            if (val.length > 0 && val.charAt(0) !== '#') {
                val = '#' + val;
            }
            this.$emit('updateCanvasProp', propKey, val);
        },

        setImage(propKey, imageData) {
            const _this = this;
            this.$emit('updateCanvasProp', propKey, imageData);

            if (propKey === 'stampImage') {
                let imageRatio;
                const stampImg = new Image();

                stampImg.onload = function() {
                    imageRatio = this.naturalWidth / this.naturalHeight;
                    _this.stampImageNaturalRatio = imageRatio;
                };

                stampImg.src = imageData;
            }
        },

        toggleSection: function(event) {
            // if we want to animate this we'll need some more complicated js
            // but for now, just a simple class toggle is good
            const section = event.target.parentElement;
            section.classList.toggle('open');
        },
        resetStamp: function(){
            this.$emit('updateCanvasProp', 'stampImage', '');
            this.$emit('updateCanvasProp','stampZoomLevel', '1');
        }
    },
};
</script>
