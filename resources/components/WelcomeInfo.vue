<template>
    <div
        class="
            options
            flex flex-dir-col
            pad-r-40px pad-b-48px pad-l-40px
            overflow-x-hidden overflow-y-auto
        "
    >
        <h1 class="font-serif font-size-48px font-blue-16 text-align-center margin-b-16px">
            Welcome!
        </h1>
        <slot />
        <p class="font-w-7 margin-t-auto margin-b-0px">
            Copy & Share Link
        </p>
        <div
            class="
                flex
                justify-content-space-between
                font-size-16px
                bg-white
                border-standard border-radius-4px
                overflow-hidden
                margin-t-8px
                relative
            "
        >
            <input
                id="share-url"
                :value="linkToCopy"
                class="
                    flex-50
                    relative
                    overflow-hidden
                    whitespace-nowrap
                    pad-t-16px pad-r-8px pad-b-16px pad-l-48px
                    margin-t-0px margin-b-0px
                    font-size-16px
                    border-none
                "
                readonly
            >
            <i class="fa fa-link absolute font-gray-80" />
            <button
                type="button"
                :class="[
                    'flex-33 border-none cursor-pointer font-size-16px font-w-7 pad-r-8px pad-l-8px',
                    urlCopied
                        ? 'font-white bg-success-green hover:font-success-green hover:bg-success-green'
                        : 'font-blue-48 bg-white hover:bg-accent-gray',
                ]"
                @click.prevent="copyLink"
            >
                <i v-if="urlCopied" class="fa fa-check" />
                {{ urlCopied ? 'Copied!' : 'Copy Link' }}
            </button>
        </div>
        <textarea
            id="copy-url-textarea"
            class="opacity-0"
            :value="linkToCopy"
        />
    </div>
</template>

<script>
export default {
    props: {
        uuid: String,
        envelopesAvailable: Number,
        isLandingPage: Boolean,
    },
    data() {
        return {
            changeCopiedStatus: null,
            urlCopied: false,
            failedCopyError: false,
        };
    },

    computed: {
        builderAppPath() {
            return process.env.MIX_BUILDER_APP_URL;
        },
        linkToCopy() {
            return this.isLandingPage ? `${this.builderAppPath}/landing?slug=${this.uuid}` : `${this.builderAppPath}?slug=${this.uuid}`;
        },
    },

    methods: {
        copyLink() {
            const id = 'copy-url-textarea';
            let existsTextarea = document.getElementById(id);
            if (!existsTextarea) {
                const textarea = document.createElement('textarea');
                textarea.id = id;
                textarea.readOnly = true;
                textarea.style.position = 'fixed';
                textarea.style.top = 0;
                textarea.style.left = 0;
                textarea.style.width = '1px';
                textarea.style.height = '1px';
                textarea.style.padding = 0;
                textarea.style.border = 'none';
                textarea.style.outline = 'none';
                textarea.style.boxShadow = 'none';
                textarea.style.background = 'transparent';
                document.querySelector('body').appendChild(textarea);
                existsTextarea = document.getElementById(id);
            }

            existsTextarea.select();

            try {
                const status = document.execCommand('copy');

                if (!status) {
                    console.error('Cannot copy text');
                    this.failedCopyError = true;
                } else {
                    // Clears task queue in case user clicks
                    // "copy link" multiple times
                    clearTimeout(this.changeCopiedStatus);
                    this.failedCopyError = false;
                    this.urlCopied = true;
                    this.changeCopiedStatus = setTimeout(() => {
                        this.urlCopied = false;
                    }, 3000);
                }
            } catch (err) {
                console.log('Unable to copy.');
                this.failedCopyError = true;
            }
        },
    },
};
</script>
