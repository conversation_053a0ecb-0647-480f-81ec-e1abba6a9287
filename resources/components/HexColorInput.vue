<template>
    <div class="tv-hex-input">
        <label
            :for="name + '-id'"
            class="block width-100 font-charcoal font-w-7 margin-b-8px"
        >
            <slot />
        </label>
        <div class="tv-hex-input__input-wrapper">
            <input
                :id="name + '-id'"
                type="text"
                class="text-uppercase"
                placeholder="#FFF000"
                :value="value"
                maxlength="7"
                @blur="$emit('input', $event, name)"
                @keydown.enter.prevent="$emit('input', $event, name)"
            >
            <div
                :id="`${name}-id-ms-display`"
                type="color"
                class="tv-hex-input__display"
                :style="{ backgroundColor: value }"
            />
            <input
                :id="`${name}-id-display`"
                type="color"
                :value="value || '#FFFFFF'"
                class="tv-hex-input__display"
                :style="{ backgroundColor: value }"
                @change="$emit('input', $event, name)"
            >
        </div>
        <slot name="error-message" />
    </div>
</template>
<script>
export default {
    props: {
        name: String,
        value: String,
    },
};
</script>