<template>
    <div
        :id="id"
        class="tv-overlay" 
        :aria-labelledby="`${id}-title`"
        aria-hidden="true"
    >
        <div
            class="tv-overlay__backdrop"
            data-a11y-dialog-hide
        />
        <div
            class="tv-overlay__content-wrapper"
            :class="classes"
            role="document"
        >
            <div class="tv-overlay__header">
                <button
                    class="btn btn--ghost btn--close"
                    aria-label="Close dialog"
                    @click="closeDialog()"
                >
                    <svg class="tv-icon close" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24">
                        <path
                            d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                        <path d="M0 0h24v24H0z" fill="none" />
                    </svg>
                </button>
            </div>
            <div class="tv-overlay__content">
                <slot name="overlay-content" />
            </div>
            <div class="tv-overlay__actions">
                <slot name="overlay-actions" />
            </div>
        </div>
    </div>
</template>

<script>
import A11yDialog from 'a11y-dialog';

export default {
    props: {
        id: String,
        classes: String
    },

    data() {
        return {
            // store a reference to the Dialog
            dialog: null,
        };
    },

    computed: {
        
    },

    created() {
        // something
    },

    mounted() {
        // register with A11yDialog
        this.$nextTick(function () {
            const container = document.getElementById(this.id);
            this.dialog = new A11yDialog(container);
        }.bind(this));
    },

    methods: {
        openDialog() {
            // open this
            this.dialog.show();
            this.$emit('dialog-opened');
        },

        closeDialog() {
            // close this
            this.dialog.hide();
            this.$emit('dialog-closed');
        },
    },

    emits: ['dialog-opened', 'dialog-closed']
};
</script>