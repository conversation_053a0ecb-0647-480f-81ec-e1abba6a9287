<template>
    <div class="step-indicator relative">
        <div class="progress-bar">
            <div class="progress-bar-mask" v-bind:style="widthStyle"></div>
        </div>
        <div class="flex align-items-center justify-content-space-between">
            <div>
                <div
                    class="step-indicator__circle margin-center text-align-center"
                    :class="{'current': step === 1, 'finished': step > 1}"
                    @click="$emit('changeStep', 1)"
                >
                    <i v-if="step > 1" class="fa fa-check font-size-14px font-white"></i>
                </div>
                <span class="step-indicator__label">Start</span>
            </div>
            <div>
                <div
                    class="step-indicator__circle margin-center text-align-center"
                    :class="{'current': step === 2, 'finished': step > 2}"
                    @click="$emit('changeStep', 2)"
                >
                    <i v-if="step > 2" class="fa fa-check font-size-14px font-white"></i>
                </div>
                <span class="step-indicator__label">Build</span>
            </div>
            <div>
                <div
                    class="step-indicator__circle margin-center text-align-center"
                    :class="{'finished': step >= 3}"
                >
                    <i v-if="step >= 3" class="fa fa-check font-size-14px font-white"></i>
                </div>
                <span class="step-indicator__label">Finish</span>
            </div>
        </div>
    </div>
</template>
    
<script>
export default {
    props: {
        step: Number,
    },

    computed: {
        progress() {
            return this.step > 3 ? 100 : (this.step - 1) * 50 || 0;
        },

        widthStyle() {
            return {
                width: `${this.progress}%`,
            };
        },
    },

    created() {},

    methods: {},
};
</script>