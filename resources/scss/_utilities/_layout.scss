@import '_config';
@import 'display';
@import 'position';

// Layout
//
// Utilitities in this module will form the bulk of the utilities you'll use in creating pages or components. They control things like positioning, sizing, display mode, etc.
//
// Style guide: Utilities.Layout
@mixin layoutUtils($sizePrefix) {

    @include displayUtils($sizePrefix);
    @include posUtils($sizePrefix);

    // width utilities
    .#{$sizePrefix}width-100 {
        width: 100%;
    }

    .#{$sizePrefix}width-90 {
        width: 90%;
    }

    .#{$sizePrefix}width-80 {
        width: 80%;
    }

    .#{$sizePrefix}width-75 {
        width: 75%;
    }

    .#{$sizePrefix}width-67 {
        width: 67%;
    }

    .#{$sizePrefix}width-50 {
        width: 50%;
    }

    .#{$sizePrefix}width-40 {
        width: 40%;
    }

    .#{$sizePrefix}width-33 {
        width: 33%;
    }

    .#{$sizePrefix}width-25 {
        width: 25%;
    }

    .#{$sizePrefix}width-1em {
        width: 1em;
    }

    .#{$sizePrefix}width-40px {
        width: 40px;
    }

    .#{$sizePrefix}width-220px {
        width: 220px;
    }

    .#{$sizePrefix}width-auto {
        width: auto;
    }

    .#{$sizePrefix}width-0 {
        width: 0;
    }

    .#{$sizePrefix}width-24px {
        width: 24px;
    }

    .#{$sizePrefix}width-180px {
        width: 180px;
    }

    .#{$sizePrefix}width-200px {
        width: 200px;
    }

    .#{$sizePrefix}width-250px {
        width: 250px;
    }

    // height utilities
    .#{$sizePrefix}height-auto {
        height: auto;
    }

    .#{$sizePrefix}height-100 {
        height: 100%;
    }

    .#{$sizePrefix}height-16px {
        height: 16px;
    }

    .#{$sizePrefix}height-20px {
        height: 20px !important;
    }

    .#{$sizePrefix}height-24px {
        height: 24px !important;
    }

    .#{$sizePrefix}height-32px {
        height: 32px;
    }

    .#{$sizePrefix}height-45px {
        height: 45px;
    }

    .#{$sizePrefix}height-64px {
        height: 64px;
    }

    .#{$sizePrefix}height-160px {
        height: 160px;
    }

    .#{$sizePrefix}height-220px {
        height: 220px;
    }

    .#{$sizePrefix}height-1em {
        height: 1em;
    }

    .#{$sizePrefix}max-height-100px {
        max-height: 100px;
    }

    .#{$sizePrefix}max-height-200px {
        max-height: 200px;
    }

    // min-width 
    .#{$sizePrefix}min-width-auto {
        min-width: auto;
    }

    .#{$sizePrefix}min-width-0px {
        min-width: 0px;
    }

    .#{$sizePrefix}min-width-32px {
        min-width: 32px !important;
    }

    .#{$sizePrefix}min-width-150px {
        min-width: 150px;
    }

    .#{$sizePrefix}min-width-200px {
        min-width: 200px;
    }

    .#{$sizePrefix}min-width-300px {
        min-width: 300px;
    }

    // max-width
    .#{$sizePrefix}max-width-none {
        max-width: none;
    }

    .#{$sizePrefix}max-width-50 {
        max-width: 50%;
    }

    .#{$sizePrefix}max-width-100 {
        max-width: 100%;
    }

    .#{$sizePrefix}max-width-100px {
        max-width: 100px;
    }

    .#{$sizePrefix}max-width-150px {
        max-width: 150px;
    }

    .#{$sizePrefix}max-width-200px {
        max-width: 200px;
    }

    .#{$sizePrefix}max-width-300px {
        max-width: 300px;
    }

    .#{$sizePrefix}max-width-350px {
        max-width: 350px;
    }

    .#{$sizePrefix}max-width-400px {
        max-width: 400px;
    }

    .#{$sizePrefix}max-width-500px {
        max-width: 500px;
    }

    .#{$sizePrefix}max-width-640px {
        max-width: 640px;
    }

    .#{$sizePrefix}max-width-720px {
        max-width: 720px;
    }

    .#{$sizePrefix}max-width-750px {
        max-width: 750px;
    }

    .#{$sizePrefix}max-width-900px {
        max-width: 900px;
    }

    .#{$sizePrefix}max-width-1050px {
        max-width: 1050px;
    }

    .#{$sizePrefix}max-width-1280px {
        max-width: 1280px;
    }

    .#{$sizePrefix}max-width-1440px {
        max-width: 1440px;
    }

    // height utilities
    .#{$sizePrefix}height-auto {
        height: auto;
    }

    .#{$sizePrefix}height-100 {
        height: 100%;
    }

    .#{$sizePrefix}height-90 {
        height: 90%;
    }

    .#{$sizePrefix}height-0 {
        height: 0;
    }

    .#{$sizePrefix}height-1px {
        height: 1px;
    }

    .#{$sizePrefix}height-32px {
        height: 32px;
    }

    // grid layout utilities
    .#{$sizePrefix}grid-gap-24px {
        grid-gap: 24px;
    }

    .#{$sizePrefix}grid-col-span-full {
        grid-column: 1 / -1;
    }

    // opacity
    .#{$sizePrefix}opacity-0 {
        opacity: 0;
    }

    .#{$sizePrefix}opacity-1 {
        opacity: 1;
    }

    // object-fit
    .#{$sizePrefix}obj-fit-contain {
        object-fit: contain;
    }

    // Overflow
    //
    // responsive: true
    //
    // A series of overflow utility classes
    // 
    // | Class Name       | CSS Values       |
    // |------------------|------------------|
    // | .overflow-auto | overflow: auto |
    // | .overflow-hidden | overflow: hidden |
    // | .overflow-visible | overflow: visible |
    // | .overflow-x-hidden | overflow-x: hidden |
    // | .overflow-y-hidden | overflow-y: hidden |
    // | .overflow-x-auto | overflow-x: auto |
    // | .overflow-y-auto | overflow-y: auto |
    // | .overflow-x-scroll | overflow-x: scroll |
    // | .overflow-y-scroll | overflow-y: scroll |
    //
    // Style guide: Utilities.Layout.Overflow
    .#{$sizePrefix}overflow-auto {
        overflow: auto;
    }

    .#{$sizePrefix}overflow-hidden {
        overflow: hidden;
    }

    .#{$sizePrefix}overflow-visible {
        overflow: visible;
    }

    .#{$sizePrefix}overflow-x-hidden {
        overflow-x: hidden;
    }

    .#{$sizePrefix}overflow-y-hidden {
        overflow-y: hidden;
    }

    .#{$sizePrefix}overflow-x-auto {
        overflow-x: auto;
    }

    .#{$sizePrefix}overflow-y-auto {
        overflow-y: auto;
    }

    .#{$sizePrefix}overflow-x-scroll {
        overflow-x: scroll;
    }

    .#{$sizePrefix}overflow-y-scroll {
        overflow-y: scroll;
    }

    .#{$sizePrefix}overflow-x-visible {
        overflow-x: visible;
    }

    .#{$sizePrefix}overflow-y-visible {
        overflow-y: visible;
    }
}

@each $size, $value in $breakpoints {
    @if str-length($size) > 0 {
        $sizePrefix: '#{$size}-';

        @media screen and (min-width: $value) {
            @include layoutUtils($sizePrefix)
        }
    }

    @else {
        @include layoutUtils('')
    }
}

// transforms
.transform-rotate-90 {
    transform: rotate(90deg);
}

.transform-rotate-180 {
    transform: rotate(180deg);
}

.ease-125ms {
    transition: transform 0.125s ease-out;
}

// box-shadow properties
.box-shadow {
    box-shadow: 0px 3px 4px rgba(0, 0, 0, 0.3);
}

.box-shadow-center {
    box-shadow: 0px 1px 6px rgba(0, 0, 0, 0.3);
}

.whitespace-nowrap {
    white-space: nowrap;
}

// z-index
.z-idx-1 {
    z-index: 1;
}

.z-idx-2 {
    z-index: 2;
}

.z-idx-10 {
    z-index: 10;
}

// break out of parent container without breaking document flow
.full-bleed {
    width: 100vw;
    margin-left: 50%;
    transform: translateX(-50%);
}

.hover\:gentle-grow {
    transition: transform 0.125s $easeOut;

    &:hover {
        cursor: pointer;
        transform: scale(1.0375);
    }
}

// hover effect to fade sibling elements
.fade-out-siblings {
    pointer-events: none;

    &:hover > * {
        opacity: 0.6;
    }

    > * {
        pointer-events: auto;
        transition: opacity 0.2s ease-out, transform 0.2s ease-out;

        &:hover {
            transform: scale(1.0375);
            opacity: 1;
        }
    }
}
