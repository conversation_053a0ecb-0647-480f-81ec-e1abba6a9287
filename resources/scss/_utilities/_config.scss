/**
 * Configuration file to be imported to each utility and component SCSS file
 * Contains maps and vars for use in generating the full utility library
 */

@import '../_variables';

// this map allows us to dynamically generate padding and margin
// class names using the first letter of the pad / margin side as a shorthand
$marginKeys: ('t': 'top',
    'r': 'right',
    'b': 'bottom',
    'l': 'left',
);

// list of color variables grouped by color, in order of ascending brightness
// this is used to generate the standard set of utility classes from the default palette
// not every color variable should necessarily go in here, only ones that should contribute
// to the utility library
$colorMap: ('black': $black,
    'gray-16': $gray-16,
    'gray-40': $gray-40,
    'gray-45': $gray-45,
    'gray-80': $gray-80,
    'gray-90': $gray-90,
    'gray-95': $gray-95,
    'gray-98': $gray-98,
    'gray-99': $gray-99,
    'white': $white,
    'blue-16': $blue-16,
    'blue-30': $blue-30,
    'blue-48': $blue-48,
    'blue-90': $blue-90,
    'blue-95': $blue-95,
    'purple-16': $purple-16,
    'purple-30': $purple-30,
    'purple-90': $purple-90,
    'red-30': $red-30,
    'red-45': $red-45,
    'red-90': $red-90,
    'green-30': $green-30,
    'green-48': $green-48,
    'green-60': $green-60,
    'green-75': $green-75,
    'green-97': $green-97,
    'light-green': $light-green,
    'orange-16': $orange-16,
    'orange-45': $orange-45,
    'orange-75': $orange-75,
    'orange-90': $orange-90,
    'orange-97': $orange-97,
    // TODO: remove these colors from the standard map?
    'moss-green': $moss-green,
    'pine-tree': $pine-tree,
    'odder-green': $odder-dark,
)
