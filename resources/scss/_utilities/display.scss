// Display
//
// a11y: If you must hide something visually and need it to be accessible to screen readers, use the <code>sr-only</code> utility. You can undo that hiding at a different breakpoint with <code>not-sr-only</code>.
//
// responsive: true
//
// Utility classes for controlling the display property and element visibility
//
// | Class Name       | CSS Values       |
// |------------------|------------------|
// | .block | display: block; |
// | .inline | display: inline; |
// | .inline-block | display: inline-block; |
// | .table-cell | display: table-cell; |
// | .flex | display: flex; |
// | .inline-flex | display: inline-flex; |
// | .grid | display: grid; |
// | .none | display: none; |
// | .vis-visible | visibility: visible; |
// | .vis-hidden | visibility: hidden; |
// | .sr-only | (many) |
// | .not-sr-only | (many) |
//
// Style guide: Utilities.Layout.Display
@mixin displayUtils($sizePrefix) {

    // display classes
    .#{$sizePrefix}block {
        display: block;
    }

    .#{$sizePrefix}inline {
        display: inline;
    }

    .#{$sizePrefix}inline-block {
        display: inline-block;
    }

    .#{$sizePrefix}table-cell {
        display: table-cell;
    }

    .#{$sizePrefix}flex {
        display: flex;
    }

    .#{$sizePrefix}inline-flex {
        display: inline-flex;
    }

    .#{$sizePrefix}grid {
        display: grid;
    }

    .#{$sizePrefix}none {
        display: none;
    }

    .#{$sizePrefix}vis-visible {
        visibility: visible;
    }

    .#{$sizePrefix}vis-hidden {
        visibility: hidden;
    }

    .#{$sizePrefix}sr-only {
        position: absolute;
        width: 1px;
        height: 1px;
        padding: 0;
        margin: -1px;
        overflow: hidden;
        clip: rect(0, 0, 0, 0);
        white-space: nowrap;
        border-width: 0;
    }

    .#{$sizePrefix}not-sr-only {
        position: static;
        width: auto;
        height: auto;
        padding: 0;
        margin: 0;
        overflow: visible;
        clip: auto;
        white-space: normal;
    }
}
