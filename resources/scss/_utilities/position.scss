
// Position
//
// responsive: true
//
// Utility classes for controlling the position property, as well as common positioning values and alignment
//
//
// | Class Name       | CSS Values       |
// |------------------|------------------|
// | .relative | position: relative; |
// | .absolute | position: absolute; |
// | .fixed | position: fixed; |
// | .sticky | position: sticky; <br/> position: -webkit-sticky; |
// | .top-0 | top: 0; |
// | .right-0 | right: 0; |
// | .bottom | bottom: 0; |
// | .left | left: 0; |
// | .v-align-top | vertical-align: top; |
// | .v-align-middle | vertical-align: middle; |
// | .v-align-bottom | vertical-align: bottom; |
// | .float-left | float: left; |
// | .float-right | float: right; |
//
// Style guide: Utilities.Layout.Position
@mixin posUtils($sizePrefix) {
    .#{$sizePrefix}relative {
        position: relative;
    }
    
    .#{$sizePrefix}absolute {
        position: absolute;
    }
    
    .#{$sizePrefix}fixed {
        position: fixed;
    }
    
    .#{$sizePrefix}sticky {
        position: -webkit-sticky;
        position: sticky;
    }

    .#{$sizePrefix}static {
        position: static;
    }
    
    .#{$sizePrefix}top-0 {
        top: 0;
    }
    
    .#{$sizePrefix}top-2px {
        top: 2px;
    }
    
    .#{$sizePrefix}top-9px {
        top: 9px;
    }
    
    .#{$sizePrefix}top-11px {
        top: 11px;
    }
    
    .#{$sizePrefix}top-12px {
        top: 12px;
    }
    
    .#{$sizePrefix}top-16px {
        top: 16px;
    }
    
    .#{$sizePrefix}right-0 {
        right: 0;
    }
    
    .#{$sizePrefix}right-2px {
        right: 2px;
    }
    
    .#{$sizePrefix}right-16px {
        right: 16px;
    }
    
    .#{$sizePrefix}right-17px {
        right: 17px;
    }
    
    .#{$sizePrefix}right-19px {
        right: 19px;
    }
    
    .#{$sizePrefix}bottom-0 {
        bottom: 0;
    }
    
    .#{$sizePrefix}bottom-16px {
        bottom: 16px;
    }
    
    .#{$sizePrefix}left-0 {
        left: 0;
    }

    .#{$sizePrefix}v-align-top {
        vertical-align: top;
    }

    .#{$sizePrefix}v-align-middle {
        vertical-align: middle;
    }

    .#{$sizePrefix}v-align-bottom {
        vertical-align: bottom;
    }

    .#{$sizePrefix}float-left {
        float: left;
    }

    .#{$sizePrefix}float-right {
        float: right;
    }
}