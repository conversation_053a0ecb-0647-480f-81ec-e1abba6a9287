// Interaction
//
// A series of interaction utilities.
//
// Style guide: Utilities.Interaction

// Cursor
//
// Utilities to control cursor behavior
// 
// | Class Name       | CSS Values       |
// |------------------|------------------|
// | .cursor-pointer | cursor: pointer; |
// | .cursor-auto | cursor: auto; |
// | .pointer-events-auto | pointer-events: auto; |
// | .pointer-events-none | pointer-events: none; |
//
// Style guide: Utilities.Interaction.Cursor
.cursor-pointer {
    cursor: pointer;
}

.cursor-auto {
    cursor: auto;
}

.pointer-events-auto {
    pointer-events: auto;
}

.pointer-events-none {
    pointer-events: none;
}

// Focus
//
// Utilities for adjusting focus behavior. Most focus behavior is defined at the component level
// but sometimes we need to make situational adjustments. Also provides a default focus state to
// interactive elements that need a basic focus outline
// 
// | Class Name       | CSS Values       |
// |------------------|------------------|
// | .focus-default | outline: 2px solid currentColor |
// | .focus-inside | outline-offset: -2px; |
//
// Style guide: Utilities.Interaction.Focus
.focus-default:focus {
    outline: 2px solid currentColor;
    outline-offset: 2px;
}

.focus-inside:focus {
    outline-offset: -2px;
}
