// use sass built-ins
@use 'sass:math';

// import fonts and variables first
@import '_variables';
@import '_fonts';

// import shared component styles 
@import '~@evertrue/tv-components/dist/components/AssetTile/AssetTile.min.css';

// import css components
@import '_components/button';
@import '_components/overlay';
@import '_components/radio-button';

// import utilities, these should satisfy the bulk of our styling needs
@import '_utilities/_interaction';
@import '_utilities/_layout';
@import '_utilities/backgrounds';
@import '_utilities/borders';
@import '_utilities/flexbox';
@import '_utilities/spacing';
@import '_utilities/typography';

// import component styles
@import 'vue-components/step-indicator';
@import 'vue-components/hex-color-input.scss';
@import 'vue-components/help-tip.scss';
@import 'vue-components/file-uploader';
@import 'vue-components/envelope-form';
@import 'vue-components/envelope-preview';
@import 'vue-components/landing-form';
@import 'vue-components/landing-preview';


// define some app-wide CSS
* {
    box-sizing: border-box;
}

body {
    background-image: url($thankview-s3-assets-path + 'assets/env_builder/img/env_builder_background_blue.jpg');
    font-family: 'Lato', sans-serif;
    // clear margins
    margin: 0;
    // start by ensuring we fill the height of the viewport
    min-height: 100vh;
    min-height: -webkit-fill-available; // this prevents funny things happening on iOS
}

p {
    line-height: 1.5;
}

input,
textarea {
    font-family: inherit;
}

.header__back {
    left: 24px;
}

.wrapper {
    min-width: 1000px;

    grid-template-columns: 420px 1fr;
    -ms-grid-columns: 420px 1fr;
    grid-template-rows: 108px minmax(560px, 60vh);
    -ms-grid-rows: 108px minmax(560px, 60vh);

    &.build,
    &.finish {
        grid-template-rows: 108px minmax(600px, 67vh);
        -ms-grid-rows: 108px minmax(600px, 67vh);
    }

    &.back-flap {
        grid-template-rows: 108px minmax(620px, 70vh);
        -ms-grid-rows: 108px minmax(620px, 70vh);
    }
}

.step-indicator {
    grid-column: 1;
    grid-row: 1;
}

.options {
    grid-column: 1;
    -ms-grid-column: 1;
    // grid-row-start: 2;
    grid-row: 2;
    -ms-grid-row: 2;
    border-right: 1px solid $gray-80;
}

.preview-area {
    grid-row: 1 / -1;
    -ms-grid-row: 1;
    -ms-grid-row-span: 2;
    grid-column: 2;
    -ms-grid-column: 2;
}

#share-url {
    border-right: 1px solid $gray-80;

    + .fa-link {
        left: 16px;
        top: 16px;
    }
}

/*
 * Style for standard text input boxes
 */
.input-standard {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid $gray-80;
    border-radius: 3px;
    color: #000;
    box-sizing: border-box;
    font-size: 1rem;
    font-weight: 500;

    &:placeholder-shown {
        color: $gray-45;
    }

    &:focus {
        outline: none;
        border-color: $blue-48;
    }
}

// required input styling
.required label,
label.required {
    &::after {
        color: $red-45;
        content: '*';
    }
}

// no utility for border-bottom so we target form sections here
.collapsible-form > div {
    height: 56px;
    overflow: hidden;
    border-bottom: 1px solid $gray-80;

    &.open {
        height: auto;
        overflow: visible;

        h2 {
            .fa-chevron-down {
                transform: rotateX(180deg);
            }
        }
    }

    h2 {
        .fa-chevron-down {
            transition: transform 0.125s ease-out;
            transform: rotateX(0deg);
            transform-style: preserve-3d;
        }

        &:hover {
            .fa-chevron-down {
                color: $gray-80;
            }
        }
    }
}

#background-uploader .file-uploader {
    min-height: 250px;
}

.list-style-none {
    list-style: none;
}

#copy-url-textarea {
    height: 0;
}

.app-mobile-warning {
    display: none;
}

@media only screen and (hover: none) and (pointer: coarse) and (max-width: 999px) {
    .wrapper {
        display: none;
    }

    .app-mobile-warning {
        display: block;
    }
}
