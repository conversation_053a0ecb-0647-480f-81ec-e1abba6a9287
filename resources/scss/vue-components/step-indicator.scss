.step-indicator {
    padding: 32px 40px;
    border-right: 1px solid $gray-80;
    border-bottom: 1px solid $gray-80;
}

.step-indicator__circle {
    position: relative;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background-color: $gray-80;
    transition: background-color 0.3s linear;

    &::after {
        position: absolute;
        display: block;
        content: '';
        width: 8px;
        height: 8px;
        top: 8px;
        left: 8px;
        border-radius: 50%;
        background-color: $gray-98;
    }

    &.current {
        background-color: $blue-16;
    }

    &.finished {
        background-color: $green-48;

        &::after {
            display: none;
        }
    }

    .fa-check {
        line-height: 26px;
    }
}

.progress-bar {
    position: absolute;
    height: 8px;
    background: $gray-80;
    margin: 0 40px;
    top: 40px;
    left: 12px;
    right: 12px;
}

.progress-bar-mask {
    background: $green-48;
    height: 8px;
    width: 0%;
    transition: width 0.3s linear;
}
