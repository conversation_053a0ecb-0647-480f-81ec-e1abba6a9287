// container should look like a fake browser window
.landing-preview__container {
    min-height: 300px;
    max-height: 620px;
    box-shadow: 0 4px 4px rgba(0, 0, 0, 0.25);
    border-radius: 4px;
}

.landing-preview__container--tablet {
    max-width: 560px;

    .landing-preview__bg-img {
        height: 500px;
    }

    .landing-preview__player {
        max-width: 320px;
        margin: 48px auto;
    }

    .landing-preview__content-gradient {
        top: 240px;
    }
}

.landing-preview__container--mobile {
    max-width: 320px;

    .landing-preview__bg-img {
        height: 320px;
    }

    .landing-preview__player {
        max-width: 280px;
        margin: 40px auto;
    }

    .landing-preview__content-gradient {
        height: 400px;
        top: 160px;
    }

    // slightly shrink appearance of buttons to fake smaller perspective
    .btn {
        font-size: 14px;
        padding: 8px 14px;
    }

    .landing-preview__text-container {
        font-size: 14px;
    }
}

.landing-preview__size-controls {
    .btn {

        &:hover {
            background-color: $purple-90;
            color: $purple-16;
        }

        &:focus {
            outline-color: $purple-30;
        }

        &[aria-selected="true"] {
            color: #fff;
            background-color: $purple-16;
        }
    }

    .btn__tooltip {
        // set bg color without utility class to
        // avoid property transition conflict
        background-color: $purple-30;
    }
}

.landing-preview__bg-img {
    // background properties
    // these are technically configurable, but not yet exposed to users
    // if added to the form as options, remove these and bind in component template
    background-position: top center;
    background-size: cover;
    background-repeat: no-repeat;

    height: 590px;
    // height of the header, minus 8px to prevent bg from bleeding through the bottom gradient
    top: 32px;
}

.landing-preview__header {
    width: 100%;
    height: 40px;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
    z-index: 3;
}

.landing-preview__header-logo {
    height: 27px;
    max-height: 40px;
    margin-left: 2%;
}

.landing-preview__player {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 64px auto;
    width: 75%;
    max-width: 360px;
    aspect-ratio: 4 / 3; // TODO: use better supported method
    background-color: #aaa;
    box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.25);
}

.landing-preview__player-play {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    background-color: $blue-48;
    color: #fff;

    .fa-play {
        // make play icon appear more correctly centered
        margin-left: 2px;
    }
}

.landing-preview__text-container {
    text-align: left;
    max-width: 540px;
    margin: 24px auto;
    font-size: 16px;
}

.landing-preview__content-gradient {
    background-image: url($thankview-assets-path + 'img/bkgds/gradient_compressed.png');
    background-size: auto 100%;
    background-repeat: repeat-x;
    position: absolute;
    left: 0;
    width: 100%;
    height: 450px;
    top: 345px;
    z-index: 1;
}

/**
* Analogous to the "video-final" div on real landing pages, contains the real content
*/
.landing-preview__video-final {
    position: relative;
    z-index: 1;
}

.landing-preview__secondary-button {}
