.tv-hex-input__input-wrapper {
    position: relative;
    overflow: hidden;
    width: 100%;
    padding: 12px 16px;
    line-height: 1;
    border: 1px solid $gray-80;
    border-radius: 3px;
    color: #000;
    background-color: #fff;

    &:focus-within {
        border-color: $blue-48;
    }

    // section that displays the currently entered color
    .tv-hex-input__display {
        position: absolute;
        top: 0;
        right: 0;
        bottom: 0;
        width: 32px;
        border: none;
    }

    div.tv-hex-input__display {
        box-shadow: -2px 0px 4px rgba(0, 0, 0, 0.2);
    }

    input {
        width: calc(100% - 40px);
        font-size: 1rem;
        font-weight: 500;
        padding: 0;
        border: none;

        &.tv-hex-input__display {
            -webkit-appearance: none;
            height: 100%;

            &::-webkit-color-swatch-wrapper {
                padding: 0;
            }

            &::-webkit-color-swatch {
                border: none;
            }
        }

        &:focus {
            outline: none;
        }
    }

    ::placeholder {
        color: $gray-80;
    }
}

@media all and (-ms-high-contrast: none), (-ms-high-contrast: active) {

    /* IE10+ CSS styles go here */
    input.tv-hex-input__display {
        display: none;
    }
}
