.stamp-preview {
    cursor: pointer;
    z-index: 2;
    font-size: 10px;
    height: 150px;
    max-height: 400px;
    max-width: 100%;
    background-color: $gray-80;
    width: 100%;
}

.build-form__input-wrapper {
    position: relative;
    overflow: hidden;
    width: 100%;
    padding: 12px 16px;
    line-height: 1;
    border: 1px solid $gray-80;
    border-radius: 3px;
    color: #000;
    background-color: #fff;

    &:focus-within {
        border-color: $blue-48;
    }

    input {
        width: 100%;
        font-size: 1rem;
        font-weight: 500;
        padding: 0;
        border: none;

        &:focus {
            outline: none;
        }
    }
}

.view-select {
    // flex: 0 0 65px;
    // FOR DEV DEBUGGING
    flex: 0 0 85px;
    height: 500px;
    overflow-y: scroll;

    & > div {
        border: 1px transparent solid;
        height: 65px;
        width: 65px;

        &.bg-blue-90 {
            border: 1px $blue-48 solid;
        }
    }

    &::-webkit-scrollbar {
        -webkit-appearance: none;
        appearance: none;
    }
}

.image-edit-border {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;

    button {
        width: 32px;
        height: 32px;
    }
}

.image-remove {
    right: 8px;
}

.image-zoom-in {
    right: 42px;
    bottom: 8px;
}

.image-zoom-out {
    right: 8px;
    bottom: 8px;
}

.crop-frame {
    width: 236px;
    height: 150px;
    border: 1px solid red;
    position: absolute;
    left: 50%;
    margin-left: -118px;
}

.options {
    .fa-info-circle {
        line-height: 24px;
    }
}
