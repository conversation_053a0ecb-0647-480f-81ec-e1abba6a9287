// Supports check to prevent old browsers from having broken radio buttons
// They'll just get their default radio buttons instead
@supports(-webkit-appearance: none) {
    .tv-radio-button {
        -webkit-appearance: none;
        /* Style the radio button container. */
        width: 18px; // ideally we'd use (r)ems, but browsers round differently so we must use fixed pixel sizes for consistent rendering
        height: 18px;
        border-radius: 50%;
        border: 2px solid $gray-80;
        margin-top: .5rem;
        margin-bottom: .5rem;
        padding: 0;
        display: inline-block;
        vertical-align: middle;
        cursor: pointer;
    }

    .tv-radio-button:checked {
        /* Prepare for placing the new checkmark. */
        position: relative;
        /* Do a bug fix to keep iOS from adding dark background. */
        background: none;
        border-color: $blue-48;
    }

    .tv-radio-button:checked::after {
        /* Place (position) the new “filled-in” blob. */
        position: absolute;
        top: 2px; // ideally we'd use (r)ems, but browsers round differently so we must use fixed pixel sizes for consistent rendering
        left: 2px;
        width: 10px;
        height: 10px;
        background: $blue-48;
        border-radius: 50%;
        content: "";
    }

    /* Focus styles. */
    /* TODO: add focus styles */
    // fieldset:focus-within  {
    //     border-color: #0b8663;
    // }
    // fieldset:focus-within legend {
    //     color: #0b8663;
    // }
    //  input[type="radio"]:focus, 
    // fieldset input[type="radio"]:focus {
    //     border: 1px solid teal;
    // }

    .tv-radio-button + label {
        display: inline-block;
        vertical-align: middle;
        padding: 0.5rem 0 0.5rem 0.25rem;
        cursor: pointer;
    }

    @media screen and (min-width: map-get($breakpoints, 'sm')) {
        .tv-radio-button {
            width: 20px;
            height: 20px;
        }

        .tv-radio-button:checked::after {
            width: 12px;
            height: 12px;
        }
    }
}
