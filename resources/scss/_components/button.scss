// Button
//
// a11y: It's important to know when something is visually presented as a button vs actually a button. If clicking the "button" takes the user to a new page, use an <code>a</code> with these classes applied. Otherwise, use a <code>button</code>. If your button only contains an icon, <a href="https://www.sarasoueidan.com/blog/accessible-icon-buttons/">read this article about accessible icon-only buttons</a>.
//
// Most buttons in our UI begin with this base class. Use case modifiers exist for base buttons and the other variants.
//
// .no-scale       - Prevents the gentle scale up on hover.
// .btn--small  - Reduces padding and font size to make the button smaller
// .btn--large  - Increases padding and font size to make the button larger
// .btn--primary - Buttons for the primary action(s) users can take in a given view
// .btn--primary-alt - Alternate color scheme for primary actions
// .btn--secondary - Buttons for the secondary action(s) users can take in a given view
// .btn--secondary-alt - Alternate color scheme for secondary actions
// .btn--tertiary - Buttons for the tertiary action(s) users can take in a given view
// .btn--cancel - Color scheme for cancel buttons
// .btn--destructive - Color scheme for destructive actions such as deleting data
// .btn--green-97 - Sets color to green-97
// .btn--orange-90 - Sets color to orange-90
// .btn--unmute-video - White button with dropshadow, for display over video content
// .btn:disabled - default styles for disabled buttons
// 
//
// Markup:
// <button type="button" class="btn {{modifier_class}}">Click Me</button>
// <button type="button" class="btn {{modifier_class}}">
//     <i class="fa fa-lightbulb btn__icon-left"></i> I have an icon!
// </button>
// <a href="#" class="btn {{modifier_class}}">A Link That Looks Like a Button</a>
//
// Style guide: Components.Button
%btn,
.btn {
    display: inline-block;
    vertical-align: middle;
    font-size: 16px;
    line-height: 1.25em;
    padding: 10px 16px;
    border-radius: 100px; // sufficiently large border radius to achieve "pill" shape
    border: none;
    // don't love this default text color, but without it lots of landing pages break
    // may want to consider other solutions to allow not setting a default on this base class
    color: #fff;
    background-color: $gray-16; // default to a dark background to ensure the base style is accessible
    font-weight: 400;
    text-align: center;
    // transition just specific properties to prevent transitioning properties for focus states (like outline)
    transition: background-color 0.125s $easeOut, color 0.125s $easeOut, transform 0.125s $easeOut;
    text-decoration: none;

    // some global hover styles for buttons
    &:hover {
        cursor: pointer;
        transform: scale(1.0375);
    }

    // add scale effect unless 'no-scale' class included
    &.no-scale:hover {
        transform: scale(1);
    }

    // begin default focus styles
    &:focus {
        outline-color: $blue-48;
        outline-offset: 2px;
        outline-width: 2px;
        outline-style: solid;
    }

    // disabled styles
    &:disabled {
        background-color: $gray-90;
        color: $gray-40;

        &:hover {
            cursor: auto;
            transform: none;
            background-color: $gray-90;
            color: currentColor;
        }

        &:focus {
            background-color: #DDDDDE;
            outline-color: $gray-40;
            color: currentColor;
        }
    }

    // don't let icons in buttons intercept click events
    > .fa {
        pointer-events: none;
    }
}

// icon element to the left of button text
.btn__icon-left {
    margin-right: 8px;
}

// icon element to the right of button text
.btn__icon-right {
    margin-left: 8px;
}

// "thin" variant
.btn--small {
    padding: 5px 16px;
}

// larger variant
.btn--large {
    font-size: 20px;
    padding: 12px 24px;
}

// Ghost Button
//
// A variant of our button that loses the pill shape and appears to just be text.
// Used mostly when a button is the semantically correct element, but visually we want text.
//
// Markup:
// <button type="button" class="btn btn--ghost">Click Me</button>
//
// Style guide: Components.Button.Ghost
%btn--ghost,
.btn--ghost {
    border: none;
    border-radius: 2px;
    background: transparent;
    color: inherit;
    padding-right: 0;
    padding-left: 0;

    &:hover {
        transform: none;
    }
}

// Icon-Only Button
//
// A variant of our button that loses the pill shape and primarily gets its shape from an icon. Often includes a <a href="#kssref-components-button-with-tooltips">tooltip</a> revealed on hover
//
// Markup:
// <button class="btn btn--icon-only hover:bg-gray-95">
// <i class="fa fa-edit font-size-24px" aria-hidden="true"></i>
// </button>
//
// Style guide: Components.Button.Icon
.btn--icon-only {
    background: transparent;
    border: none;
    position: relative;
    color: inherit;
    padding: 8px;
    border-radius: 3px;
    min-width: 40px;
    text-align: center;
}

// Circle Button
//
// A circle button, often used inline with the "pill shape" buttons for a closer aesthetic.
// Typically includes just an icon, and is fixed in size to 40px x 40px
//
// Markup:
// <button class="btn btn--circle">
// <i class="fa fa-download" aria-hidden="true"></i>
// </button>
//
// Style guide: Components.Button.Circle
.btn--circle {
    border-radius: 100%;
    min-width: 40px;
    width: 40px;
    height: 40px;
    line-height: 40px;
    padding: 0;
}

// TODO: remove this at the component level
.btn--input {
    padding: 0;
    border-radius: 0 4px 4px 0;
    height: 40px;
    line-height: 40px;

    .fa {
        margin: 0;
        padding-left: 0.5px;
    }
}

// french blue
%btn--primary,
.btn--primary {
    background-color: $blue-48;
    color: #fff;

    &:hover, &:active {
        background-color: $blue-30;
    }

    &:focus {
        background-color: #1C5AEA;
        outline-color: #1C5AEA;
    }
}

// light blue
.btn--primary-alt {
    background-color: $blue-90;
    color: $blue-16;

    &:hover {
        color: #fff;
        background-color: $blue-16;
    }

    &:focus {
        background-color: #A7B9DD;
        outline-color: $blue-16;
    }
}

// light purple
.btn--secondary {
    background-color: $purple-90;
    color: $purple-16;

    &:hover {
        background-color: $purple-16;
        color: #fff;
    }

    &:focus {
        background-color: #C4ACE1;
        outline-color: $purple-16;
    }
}

// white
.btn--secondary-alt {
    background-color: #fff;
    color: $blue-16;

    &:hover {
        background-color: $blue-16;
        color: #fff;
    }

    &:focus {
        background-color: #CED3DD;
        outline-color: $blue-16;
    }
}

// navy blue
.btn--tertiary {
    background-color: $blue-16;
    color: #fff;

    &:hover {
        background-color: $blue-16;
    }

    &:focus {
        background-color: #0A2463;
        outline-color: #0A2463;
    }
}

// gray outline style
.btn--cancel {
    background-color: $white;
    color: $gray-45;
    border: 1px solid $gray-45;
    // must reduce padding to account for border
    // TODO: refactor this, since this will conflict with small and large btn variants
    padding: 9px 16px;

    &:hover {
        background-color: $gray-45;
        color: #fff;
    }

    &:focus {
        outline-color: $gray-45;
    }
}

// red-45
.btn--destructive {
    background-color: $red-45;
    color: #fff;

    &:hover {
        background-color: $red-30;
        color: #fff;
    }

    &:focus {
        background-color: #C3294B;
        outline-color: $red-45;
    }
}

.btn--unmute-video {
    background-color: #fff;
    color: $blue-30;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);

    &:hover {
        background-color: $blue-90;
        color: $blue-16;
    }
}

.btn--orange-90 {
    background-color: $orange-90;
    color: $orange-16;

    &:hover {
        background-color: $orange-16;
        color: $orange-90;
    }
}

.btn--green-97 {
    background-color: $green-97;
    color: $green-30;

    &:hover {
        background-color: $green-48;
        color: #fff;
    }

    &:focus {
        background-color: $green-48;
        outline-color: $green-30;
    }
}

.btn--green-30 {
    background-color: $green-30;
    color: $white;

    &:hover {
        background-color: $pine-tree;
    }
}

// ThankGiving brand
.btn--pale-green {
    background-color: $pale-green;
    color: $pine-tree;

    &:hover {
        background-color: $green-97;
    }
}

// ODDER branding
.btn--odder-green {
    background-color: #388895;
    color: #FFFFFF;

    &:hover {
        background-color: #0E2A6D;
        color: #FFFFFF;
    }
}

// only used for odder page
.btn--odder {
    background-color: $odder-light;
    color: $blue-16;

    &:hover {
        background-color: $odder-dark;
        color: $odder-light;
    }
}

// Button Tooltips
//
// Buttons can support a tooltip that is revealed on hover. The tooltip should use utility classes for background colors and text colors.
//
// .btn__tooltip--extra-small - a very small tooltip
// .btn__tooltip--small - a small tooltip
// .btn__tooltip--large - a larger tooltip
// .btn__tooltip--up - tooltip should appear above the button with transition effect
// .btn__tooltip--down - tooltip should appear below the button with transition effect
// .btn__tooltip--pin-left - tooltip is pinned to the left edge of the button and extends to the right
// .btn__tooltip--pin-right - tooltip is pinned to the right edge of the button and extends to the left
//
// Markup:
// <button class="btn btn--icon hover:bg-gray-95">
// <i class="fa fa-edit font-size-24px" aria-hidden="true"></i>
// <span class="btn__tooltip {{modifier_class}} bg-blue-48 font-white">Edit</span>
// </button>
//
// Style guide: Components.Button.With Tooltips
.btn__tooltip {
    position: absolute;
    left: -85%;
    right: -85%;
    width: auto;
    display: block;
    padding: 8px;
    border-radius: 3px;
    font-size: 14px;
    text-align: center;
    line-height: 1;
    opacity: 0;
    pointer-events: none;
    transition: all 0.175s $easeOut;
    transform: translateY(0); // set default so it's transitionable

    // reveal tooltip on hover or focus
    // relies on focus-visible support so lingering clicks don't reveal tooltips
    :hover > &,
    :focus-visible > & {
        opacity: 1;
    }
}

.btn__tooltip--extra-small {
    left: 0;
    right: 0;
}

.btn__tooltip--small {
    left: -40%;
    right: -40%;
}

.btn__tooltip--large {
    left: -175%;
    right: -175%;
}

.btn__tooltip--up {
    bottom: 100%;

    :hover > &,
    :focus-visible > & {
        transform: translateY(-4px);
    }
}

.btn__tooltip--down {
    top: 100%;

    :hover > &,
    :focus-visible > & {
        transform: translateY(4px);
    }
}

.btn__tooltip--pin-left {
    left: 0;
    right: -150%;
}

.btn__tooltip--pin-right {
    left: -150%;
    right: 0;
}

/**
 * Styles for standard button groupings
 */
// TODO: replace instances of this with utility classes
.button-flex-area {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-around;
    align-items: center;

    .button {
        flex: 1 1 40%;
        margin: 8px 4px !important; // annoying to use important, but an easy solve for now

        /* when there is only 1 button, prefer shrinking */
        &:first-child:nth-last-child(1) {
            flex: 0 1 auto;
        }

        /* when there are 3 buttons, the first should be full-width */
        &:first-child:nth-last-child(3) {
            flex-basis: 100%;
        }
    }
}
