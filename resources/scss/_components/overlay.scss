$paddingIncrement: 8px;
$marginIncrement: 8px;
$topPositioning: 20%;
$headerHeight: 40px;
$backdropAnimationDuration: 200ms;
$contentAnimationDuration: 300ms;

// Overlays
// 
// A11y: Read the details on <a href="https://a11y-dialog.netlify.app/usage/markup">A11y Dialog's markup reference</a> for an explanqtion regarding accessibility of overlay markup.
// 
// Overlays in ThankView leverage a library called <a href="https://a11y-dialog.netlify.app/">A11y Dialog</a> for most of their functionality. Opening and closing is normally handled by the framework, click handlers here are for demo purposes only (see script comments in markup).
// 
// Markup: ../../../tv-styleguide/markup-files/tv-overlay.hbs
//
// Style guide: Components.Overlays

// basic positioning of the container and backdrop
.tv-overlay,
.tv-overlay__backdrop {
    // basic positioning of the container and backdrop
    position: fixed;
    left: 0;
    right: 0;
    bottom: 0;
    top: 0;
}

/**
 * 1. Make sure the dialog container and all its descendants sits on
 *    top of the rest of the page.
 * 2. Make the dialog container a flex container to easily center the
 *    dialog.
 */
.tv-overlay {
    z-index: $overlay-z-index;
    display: flex;
    align-items: center;

    /**
    * Prevent showing and focusing in hidden overlays
    */
    &[aria-hidden='true'] {
        display: none;
    }

    /**
    * Hide close button on overlays the user cannot close
    */
    &[role="alertdialog"] .btn--close {
        display: none;
    }

    /**
    * Animate entry of non-alert dialogs
    * Can't animate alert dialogs as the animation replays if the user tries to click outside it
    */
    &[role="dialog"] {
        .tv-overlay__backdrop {
            animation: backdropFade $backdropAnimationDuration both;
        }

        .tv-overlay__content-wrapper {
            // add fade animation for showing overlay
            animation: backdropFade $contentAnimationDuration $backdropAnimationDuration both;

            @media screen and (prefers-reduced-motion: no-preference) {
                // if no reduced motion preference is set, also add a small slide animation
                animation: backdropFade $contentAnimationDuration $backdropAnimationDuration both, overlayContentEnter $contentAnimationDuration $backdropAnimationDuration both;
            }
        }
    }
}

/**
*
*/
.tv-overlay__backdrop {
    background-color: rgba(0, 0, 0, 0.5);
}

/**
 * The actual dialog box
 */
.tv-overlay__content-wrapper {
    position: relative;
    width: 90%;
    max-width: 540px;
    text-align: center;
    border-radius: 6px;
    border: none;
    padding: 0px;
    margin: auto;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    align-content: flex-start;
    box-shadow: 0 2px 4px rgba(0, 0, 0, .5);
    z-index: 10000;
    color: $gray-16;
    background: #fff;
    -webkit-overflow-scrolling: touch;
    overflow-y: auto;

    // cap height at full screen height, minus net positioning offset and total vertical padding + header height
    // this ensures the overlay itself fits on the screen, and the content area is scrollable
    max-height: calc(95vh - #{($paddingIncrement * 6)} - #{$headerHeight});
}

.tv-overlay__header {
    height: $headerHeight;
    flex: 1 1 100%;
    order: 0;
    display: flex;
    align-items: center;
    z-index: 2;
    position: sticky;
    top: 0;
    background-color: #fff;
    border-top-left-radius: 6px;
    border-top-right-radius: 6px;

    .btn--close {
        width: $headerHeight;
        height: $headerHeight;
        padding: $paddingIncrement;
        margin-left: auto;
        border-radius: 8px;

        &:focus {
            outline-offset: (math.div($paddingIncrement, 2) * -1); // ensure focus outline is rendered within the overlay  
        }
    }
}

.tv-overlay__content {
    font-size: 24px;
    padding: 0px ($paddingIncrement * 3) ($paddingIncrement * 3) ($paddingIncrement * 3);
    order: 2;
    flex: 1;
    max-width: 100%;
}

.tv-overlay__header-text {
    margin: 0px;
    text-align: center;
    font-size: 24px;
    line-height: 1;
}

.tv-overlay__message {
    font-size: 16px;
    margin-top: ($marginIncrement * 2);
    margin-bottom: ($marginIncrement * 3);
}

.tv-overlay__error {
    color: $red-45;
    font-size: 14px;
    margin-top: ($marginIncrement);
    margin-bottom: ($marginIncrement * 2);
}

.tv-overlay__input {
    display: flex;
    width: 100%;

    input {
        border-color: $gray-80;
        border-radius: 4px 0 0 4px;
        border-width: 1px 0 1px 1px;
        flex: 1 1;
        font-size: 16px;
        height: 40px;
        margin-bottom: $marginIncrement;
        padding: 10px;
    }
}

.tv-overlay__actions {
    padding: 0px ($paddingIncrement * 3) ($paddingIncrement * 3) ($paddingIncrement * 3);
    flex: 1 1 100%;
    order: 2;
}

.tv-overlay__media {
    height: 250px;
    order: 1;
    -ms-flex-preferred-size: 100%;
    flex-basis: 100%;
}

/**
 * Adds a tabbed interface to overlays that spans the full width of the overlay
 * Nested selectors here to enforce use of button elements as the tab
 * TODO: needs a more proper rewrite / accessibility audit
 */
.tv-overlay__tabs {
    display: flex;
    align-content: space-between;
    margin: 0 -24px 0;
    position: sticky;
    top: 40px;
    z-index: 2;
    background-color: $white;

    button {
        flex: 1 1 auto; // just fit yourselves evenly
        font-size: 16px;
        cursor: pointer;
        background-color: $gray-90;
        padding: 4px 0 8px;
        border: none;
        border-top: 4px solid $gray-90;
        margin-right: 3px;

        &:last-child {
            margin-right: 0;
        }

        &:focus {
            outline: 2px solid $blue-48;
            outline-offset: -2px;
        }

        &.active {
            background-color: #fff;
            color: $blue-48;
            font-weight: 500;
            border-top-color: $blue-48;
        }
    }
}

// Modal Overlays
// 
// Modal overlays in ThankView are a larger variant of the standard overlay that generally feature some content and a media (decorative or otherwise) element side-by-side. They also feature a header bar with a close button, and can contain action buttons using an element with the class tv-overlay__actions. Open and close interactions work the same way as the base overlay.
// 
// Markup: ../../../tv-styleguide/markup-files/tv-overlay-modal.hbs
//
// Style guide: Components.Overlays.Modal
.tv-overlay--modal {

    .tv-overlay__content-wrapper {
        max-width: 630px;
        z-index: 9999;
    }

    .tv-overlay__header {
        border-bottom: 1px solid $gray-90;
    }

    .tv-overlay__content {
        padding-top: ($paddingIncrement * 3);
        padding-bottom: ($paddingIncrement * 3);
    }
}

// Fullscreen Overlays
// 
// Fullscreen overlays in ThankView are overlays that appear to be a full page, but don't actually navigate the user to a new page. An example is the edit video view from the video library. They feature a larger header with "back" navigation, and their content area can be treated as a full page.
// 
// Markup: ../../../tv-styleguide/markup-files/tv-overlay-fullscreen.hbs
//
// Style guide: Components.Overlays.Fullscreen
.tv-overlay--fullscreen {
    text-align: left;

    .tv-overlay__content-wrapper {
        max-width: 100%;
        width: 100%;
        height: 100%;
        max-height: 100vh;
        border-radius: 0px;
        // only use fade in for fullscreen overlays, no slide and no delay
        // use !important so we don't nest further just for this one override
        animation: backdropFade $contentAnimationDuration 0ms both !important;
    }

    .tv-overlay__header {
        height: 60px;
        padding: 0px 24px;
        background-color: $gray-98;
        font-weight: 700;
        align-items: center;
        border-radius: 0;

        // fix IE 11 and scrolling content
        position: fixed;
        left: 0;
        right: 0;

    }

    .tv-overlay__header-text {
        font-size: 36px;
        margin-top: 24px;
        margin-bottom: 24px;
        text-align: left;
    }

    .tv-overlay__content {
        padding-top: 40px; // part of IE 11 positioning fix
        height: 100%;
        width: 100%;
    }

    // put sticky positioning back when supported
    @supports(position: sticky) {
        .tv-overlay__header {
            position: sticky;
        }

        .tv-overlay__content {
            padding-top: 0;
        }
    }
}

// special fullscreen takeover loading indicator
// TODO: probably replace this with better, more in-line loading indicators
.tv-overlay--loading {
    z-index: 60000;
    background-color: rgba(0, 0, 0, 0.5);

    .tv-overlay__content {
        color: $gray-95;
        display: flex;
        justify-content: center;
        align-items: center;
        background: none !important;
    }

    .fa {
        font-size: 64px;
    }
}

@media screen and (min-width: map-get($breakpoints, 'md')) {

    .tv-overlay__content {
        padding-right: ($paddingIncrement * 4);
        padding-left: ($paddingIncrement * 4);
        -ms-flex-preferred-size: calc(50% - #{($paddingIncrement * 8)});
    }

    .tv-overlay--modal {
        .tv-overlay__content {
            padding-right: ($paddingIncrement * 5);
            padding-left: ($paddingIncrement * 5);
            -ms-flex-preferred-size: calc(50% - #{($paddingIncrement * 10)});
        }
    }

    .tv-overlay__tabs {
        margin: 0 -32px 0;
    }
}

@media screen and (min-width: map-get($breakpoints, 'lg')) {
    .tv-overlay--modal {
        .tv-overlay__content-wrapper {
            max-width: 840px;
        }
    }

    .tv-overlay__content {
        flex: 1 1 50%;
    }

    .tv-overlay__media {
        flex-basis: 50%;
        -ms-flex-preferred-size: 50%;
        order: 2;
        height: 500px;
    }
}

@media screen and (min-width: map-get($breakpoints, 'xl')) {
    .tv-overlay--modal {
        .tv-overlay__content-wrapper {
            max-width: 1040px;
        }
    }

    .tv-overlay--fullscreen {
        .tv-overlay__content {
            padding-left: ($paddingIncrement * 8);
            padding-right: ($paddingIncrement * 8);
            -ms-flex-preferred-size: calc(50% - #{($paddingIncrement * 16)});
        }
    }
}

// overlay animation
@keyframes backdropFade {
    from {
        opacity: 0;
    }
}

@keyframes overlayContentEnter {
    from {
        transform: translateY(10%);
    }
}
