
$breakpoints: ('': '', // an empty breakpoint for "all" used to generate non-sized utilities
    'sm': 576px,
    'md': 768px,
    'lg': 1024px,
    'xl': 1366px,
    'xxl': 1920px);

$black: #000;
$gray-16: #28292C;
$gray-40: #5D5E65;
$gray-45: #696A72;
$gray-80: #C6C6C9;
$gray-90: #E2E2E4;
$gray-95: #F0F1F1;
$gray-98: #F9F9FA;
$gray-99: #FCFCFC;
$white: #fff;

// primary brand colors
$blue-16: #0A2463;
$blue-30: #173FA0;
$blue-48: #3367F2;
$blue-90: #DDE1FD;
$blue-95: #EEF0FE;

// secondary colors
// purples
$purple-16: #3D0B67;
$purple-30: #6618A6;
$purple-90: #E8DEFD;
// reds
$red-30: #881F0E;
$red-45: #C8321A;
$red-90: #FDDBD9;
// greens
$green-30: #2F4F0E;
$green-48: #4E7F1C;
$green-60: #28A475;
$green-75: #81CC33;
$green-97: #E4FED6;
$light-green: #d8eacc;
// oranges
$orange-16: #3A2505;
$orange-45: #8F611A;
$orange-75: #F5A933;
$orange-90: #FDDDC2;
$orange-97: #FEF5EE;

$pale-green: #f4fded;
$moss-green: #afd9b5;
$pine-tree: #1e2414;

// ODDER BRAND COLORS
$odder-light: #ECFBF7;
$odder-dark: #357B87;
$odder-light-blue: #D7F4EE;
$odder-navy-blue: #0E2A6D;

// define where in the stacking order Intercom belongs so other elements
// can reference this to decide if they should be overlap intercom or not
$intercom-z-index: 5000;
$overlay-z-index: $intercom-z-index + 1; // by default overlays should cover intercom?

// animation timing functions
$easeIn: cubic-bezier(0.550, 0.085, 0.680, 0.530);
$easeOut: cubic-bezier(0.250, 0.460, 0.450, 0.940);

// assets path configuration - can be overridden via environment variable
$thankview-assets-path: 'https://assets.thankview.com/assets/';
$thankview-s3-assets-path: 'https://thankviews.s3.us-west-2.amazonaws.com/';
