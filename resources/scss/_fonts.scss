/**
 * Primary Font Definitions
 * Currently using Lato
 */
/* Webfont: Lato Black */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Black.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Black.woff') format('woff');
    font-style: normal;
    font-weight: 900;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Black Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-BlackItalic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-BlackItalic.woff') format('woff');
    font-style: italic;
    font-weight: 900;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Heavy */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Heavy.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Heavy.woff') format('woff');
    font-style: normal;
    font-weight: 800;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Heavy Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-HeavyItalic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-HeavyItalic.woff') format('woff');
    font-style: italic;
    font-weight: 800;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Bold */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Bold.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Bold.woff') format('woff');
    font-style: normal;
    font-weight: 700;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Bold Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-BoldItalic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-BoldItalic.woff') format('woff');
    font-style: italic;
    font-weight: 700;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Semibold */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Semibold.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Semibold.woff') format('woff');
    font-style: normal;
    font-weight: 600;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Semibold Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-SemiboldItalic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-SemiboldItalic.woff') format('woff');
    font-style: italic;
    font-weight: 600;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Medium */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Medium.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Medium.woff') format('woff');
    font-style: normal;
    font-weight: 500;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Medium Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-MediumItalic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-MediumItalic.woff') format('woff');
    font-style: italic;
    font-weight: 500;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Regular */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Regular.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Regular.woff') format('woff');
    font-style: normal;
    font-weight: 400;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Italic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Italic.woff') format('woff');
    font-style: italic;
    font-weight: 400;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Light */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-Light.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-Light.woff') format('woff');
    font-style: normal;
    font-weight: 300;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Light Italic */
@font-face {
    font-family: 'Lato';
    src: url('#{$cdn-path}/fonts/lato/LatoLatin-LightItalic.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/lato/LatoLatin-LightItalic.woff') format('woff');
    font-style: italic;
    font-weight: 300;
    font-display: swap;
    text-rendering: optimizeLegibility;
}

/* Webfont: Lato Thin */
// @font-face {
//     font-family: 'Lato';
//     src: url('#{$cdn-path}/fonts/lato/LatoLatin-Thin.woff2') format('woff2'),
//     url('#{$cdn-path}/fonts/lato/LatoLatin-Thin.woff') format('woff');
//     font-style: normal;
//     font-weight: 200;
//     font-display: swap;
//     text-rendering: optimizeLegibility;
// }

// /* Webfont: Lato Thin Italic */
// @font-face {
//     font-family: 'Lato';
//     src: url('#{$cdn-path}/fonts/lato/LatoLatin-ThinItalic.woff2') format('woff2'),
//     url('#{$cdn-path}/fonts/lato/LatoLatin-ThinItalic.woff') format('woff');
//     font-style: italic;
//     font-weight: 200;
//     font-display: swap;
//     text-rendering: optimizeLegibility;
// }

// /* Webfont: Lato Hairline */
// @font-face {
//     font-family: 'Lato';
//     src: url('#{$cdn-path}/fonts/lato/LatoLatin-Hairline.woff2') format('woff2'),
//     url('#{$cdn-path}/fonts/lato/LatoLatin-Hairline.woff') format('woff');
//     font-style: normal;
//     font-weight: 100;
//     font-display: swap;
//     text-rendering: optimizeLegibility;
// }

// /* Webfont: Lato Hairline Italic */
// @font-face {
//     font-family: 'Lato';
//     src: url('#{$cdn-path}/fonts/lato/LatoLatin-HairlineItalic.woff2') format('woff2'),
//     url('#{$cdn-path}/fonts/lato/LatoLatin-HairlineItalic.woff') format('woff');
//     font-style: italic;
//     font-weight: 100;
//     font-display: swap;
//     text-rendering: optimizeLegibility;
// }

/* calistoga-regular - latin */
@font-face {
    font-family: 'Calistoga';
    font-style: normal;
    font-weight: 400;
    src: local('Calistoga Regular'), local('Calistoga-Regular'),
    url('#{$cdn-path}/fonts/calistoga-v1-latin-regular.woff2') format('woff2'),
    url('#{$cdn-path}/fonts/calistoga-v1-latin-regular.woff') format('woff');
    font-display: swap;
}
