<template>
    <div
        class="
            wrapper
            width-90
            max-width-1280px
            bg-gray-98
            box-shadow
            border-radius-10px
            overflow-hidden
            margin-center margin-t-32px margin-b-72px
            wrapper
            grid
        "
        :class="{ build: step === 2, finish: step >= 3 }"
    >
        <step-indicator
            :step="step"
            @changeStep="changeStep"
        />
        <welcome-info
            v-if="step === 1"
            :uuid="uuid"
            :landing-pages-available="landingPagesAvailable"
            :is-landing-page="true"
        >
            <p
                v-if="!existingLandingLoading && !landingLoadError"
                class="text-align-left"
            >
                Our landing page builder is quick and easy, and will only take a few minutes to complete.
                <br><br>
                Check out some <a target="_blank" rel="noopener noreferrer" href="https://community.thankview.com/t/q6hp05d/how-to-landing-page-builder#examples-of-great-landing-page-designs">landing page examples</a> from other ThankView users.
                <br><br>
                Feel free to reach out to our Customer Support team via the live chat or email <a href="mailto:<EMAIL>"><EMAIL></a> for any questions you may have.
            </p>
            <p
                v-if="!existingLandingLoading && landingLoadError"
                class="text-align-left bg-red-90 font-red-30 border-radius-6px pad-t-8px pad-r-16px pad-b-8px pad-l-16px"
            >
                Hmmm... We couldn't find that landing page. Try selecting it again, or <a href="mailto:<EMAIL>">contact support</a>.
            </p>
            <p
                v-if="existingLandingLoading"
                class="font-size-24px text-align-center"
            >
                Loading your landing
                <br>
                page design...
                <br>
                <i class="fas fa-spinner fa-spin font-size-36px block margin-center margin-t-24px" />
            </p>
        </welcome-info>
        <landing-form
            v-else-if="step === 2"
            :landing-props="landingProps"
            :bkgd-images="bkgdImages"
            :bkgd-img-deleted="bkgdImgDeleted"
            :submission-error="submissionError"
            :submission-error-msg="submissionErrorMsg"
            :valid="$v"
            @updatePreviewProp="updatePreviewProp"
            @openNewBkgdImg="openBackgroundUploader"
            @selectBkgdImg="selectBackgroundImage"
            @deleteBkgdImage="confirmBkgdDelete"
            @reportEvent="reportEvent"
        />
        <finish-info
            v-else-if="step === 3"
            :landing-pages-available="landingPagesAvailable"
            :landing-pages-credited="landingPagesCredited"
        >
            <h1 class="font-serif font-blue-16 font-size-48px">
                Huzzah!
            </h1>
            <p>
                Your landing page is now in your portal.
                <br><br>
                Prior to using this landing page in a sent campaign, you may delete it at any time. Currently, if you’d like to make any edits, you will have to delete the landing page and create a new one.
            </p>
        </finish-info>
        <div
            v-if="step === 1"
            class="
                preview-area
                flex flex-wrap
                justify-content-center
                bg-white
                max-width-100
                overflow-hidden
                pad-t-40px pad-r-40px pad-b-40px pad-l-40px
            "
        >
            <div class="min-width-300px max-width-100 align-self-center">
                <img
                    :src="$config.assetsPath + 'img/envelope-builder-welcome.jpg'"
                    alt="Illustration of envelope construction site"
                    class="block width-100 max-width-640px"
                >
            </div>
            <step-navigation
                :step="step"
                @changeStep="changeStep"
            />
        </div>
        <landing-preview
            v-else-if="step >= 2"
            :submitting="submitting"
            :valid="$v"
            :landing-props="landingProps"
            :step="step"
            :existing-landing-id-string="existingLandingIdString"
            :parent-page="parentPage"
            @changeStep="changeStep"
            @saveLanding="saveLanding"
            @newLanding="newLanding"
        />
        <a11y-dialog
            id="background-uploader"
            ref="bgUpload"
            :classes="'max-width-750px'"
            @dialog-closed="resetNewBkgdImg"
        >
            <template v-slot:overlay-content>
                <h1
                    id="background-uploader-title"
                    class="tv-overlay__header-text text-align-left margin-b-24px"
                >
                    {{ editingBkgdImg ? 'Rename' : 'Create' }} a Background Image
                </h1>
                <form
                    name="newBkgdImgForm"
                    class="font-size-16px flex flex-wrap-no"
                    @keypress.enter.prevent="saveBackgroundImage"
                >
                    <input
                        v-model="newBkgdImg.id"
                        type="hidden"
                    >
                    <div
                        v-if="editingBkgdImg"
                        class="flex-67 pad-r-16px margin-t-8px"
                    >
                        <img
                            :src="newBkgdImg.bkgd_image"
                            alt=""
                            class="block max-width-100"
                        >
                    </div>
                    <file-uploader
                        v-if="!editingBkgdImg"
                        class="margin-t-8px border-none flex-67 pad-r-16px"
                        :class="{ 'has-error': bkgdImgError && !$v.newBkgdImg.data.required }"
                        :file-upload-id="'new_bg_img'"
                        :new-file-data="newBkgdImg.data"
                        @setImage="setNewBgImg"
                    >
                        <template v-slot:error-messages>
                            <p v-if="bkgdImgError && !$v.newBkgdImg.data.required">
                                <i class="fas fa-exclamation-circle" />
                                Please select an image.
                            </p>
                        </template>
                    </file-uploader>
                    <div class="flex-33 text-align-left height-100">
                        <label
                            for="landing-title"
                            class="block width-100 font-charcoal font-w-7 margin-b-8px required"
                        >
                            Name Your Image
                        </label>
                        <div class="build-form__input-wrapper">
                            <input
                                id="new-bkgd-img-name"
                                v-model="newBkgdImg.name"
                                type="text"
                                placeholder="My Background"
                                maxlength="20"
                            >
                        </div>
                        <div
                            v-if="bkgdImgError && !$v.newBkgdImg.name.required"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Give your background a name.
                        </div>
                        <div
                            v-if="bkgdImageDuplicateName"
                            class="font-red-45 margin-t-4px"
                        >
                            <i class="fas fa-exclamation-circle" />
                            Name must be unique.
                        </div>
                    </div>
                </form>
            </template>
            <template v-slot:overlay-actions>
                <button
                    type="button"
                    class="btn btn--primary float-right"
                    @click="saveBackgroundImage"
                >
                    <span v-if="!submittingBkgdImg">Save Background</span>
                    <span v-if="submittingBkgdImg">
                        <i class="fas fa-spinner fa-spin" />
                        Saving...
                    </span>
                </button>
            </template>
        </a11y-dialog>
        <a11y-dialog
            id="background-delete-confirm"
            ref="bgDeleteConfirm"
        >
            <template v-slot:overlay-content>
                <h1
                    id="background-delete-confirm"
                    class="tv-overlay__header-text margin-b-24px"
                >
                    Delete Background Image
                </h1>
                <p class="font-size-16px">
                    Are you sure you want to delete this image?
                </p>
            </template>
            <template v-slot:overlay-actions>
                <button
                    type="button"
                    class="btn btn--cancel margin-r-16px"
                    @click="$emit('bkgdDeleteConfirmed', false)"
                >
                    No
                </button>
                <button
                    type="button"
                    class="btn btn--primary"
                    @click="$emit('bkgdDeleteConfirmed', true)"
                >
                    Yes
                </button>
            </template>
        </a11y-dialog>
    </div>
</template>

<script>
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import A11yDialog from '../components/A11yDialog';
import FileUploader from '../components/FileUploader';
import StepIndicator from '../components/StepIndicator';
import StepNavigation from '../components/StepNavigation.vue';

import { maxLength, required, requiredIf, helpers } from 'vuelidate/lib/validators';
const isHex = helpers.regex('isHex', /^#(?:[0-9a-fA-F]{3}){1,2}$/);

const WelcomeInfo = () =>
    import(/* webpackChunkName: "welcomeChunk" */ '../components/WelcomeInfo');
const LandingForm = () =>
    import(/* webpackChunkName: "landingChunk" */ '../components/LandingForm');
const LandingPreview = () =>
    import(/* webpackChunkName: "landingChunk" */ '../components/LandingPreview');
const FinishInfo = () =>
    import(/* webpackChunkName: "finishChunk" */ '../components/FinishInfo');

function defaultLandingProps() {
    return {
        name: '',
        header_bkgd_color: null,
        header_logo: null, // becomes URL of previously saved logo, or image data of newly uploaded file
        header_logo_file: null, // raw image file data, required for saving
        bkgd_image_id: null, // id of the selected background image
        bkgd_image: null, // object representing background image, for preview only
        button_text_color: null, // cta button text color
        button_color: null, // cta button color
        button_below_text_color: null, // secondary buttons text color
        button_reply_color: null,
        button_save_color: null,
        button_share_color: null,
        cta_text_bkgd: null, // this maps to setting multiple values in the database
        hide_message_gradient: false
    };
}

export default {
    components: {
        'step-indicator': StepIndicator,
        'welcome-info': WelcomeInfo,
        'landing-form': LandingForm,
        'landing-preview': LandingPreview,
        'finish-info': FinishInfo,
        'a11y-dialog': A11yDialog,
        'file-uploader': FileUploader,
        'step-navigation': StepNavigation
    },

    data() {
        return {
            tvAppPath: '*',
            uuid: null,
            existingLandingPageId: null,
            existingLandingIdString: null,
            existingLandingLoading: false,
            landingLoadError: false,
            landingPagesAvailable: 1,
            landingPagesCredited: 1,
            step: 1,
            submitting: false,
            submissionError: false,
            submissionErrorMsg: null,
            landingProps: defaultLandingProps(),
            newBkgdImg: {
                id: null,
                name: null, // user-provided name of new image
                data: null, // actual image data
                bkgd_image: null, // file path to uploaded image, used when renaming
            },
            bkgdImages: [], // default to empty array, these are fetched from the database
            submittingBkgdImg: false, // submitting status of upload bg img form
            bkgdImageDuplicateName: false, // whether the submitted background image name is a duplicate
            bkgdImgError: false, // if there's an error when adding bg img
            bkgdImgDeleting: null, // reference to a background image object the user wants to delete
            bkgdImgDeleted: false, // if the currently selected background image was deleted, only determined when loading existing design
        };
    },

    validations: {
        landingProps: {
            name: {
                required,
                maxLength: maxLength(20)
            },
            header_bkgd_color: {
                isHex
            },
            button_text_color: {
                isHex
            },
            button_color:  {
                isHex
            },
            button_below_text_color: {
                isHex
            },
            button_reply_color: {
                isHex
            },
            button_save_color: {
                isHex
            },
            button_share_color: {
                isHex
            },
        },
        newBkgdImg: {
            name: {
                required
            },
            data: {
                required: requiredIf(function() {
                    return !this.editingBkgdImg;
                }),
            }
        }
    },

    computed: {
        editingBkgdImg() {
            return this.newBkgdImg.id !== null;
        },

        inIframe() {
            return window.self !== window.top;
        }
    },

    watch: {

    },

    created() {
        if (this.$route.query.slug) {
            this.uuid = this.$route.query.slug;
        } else {
            window.location = 'https://www.thankview.com';
        }
        if (this.$route.query.user_id) {
            this.user_id = this.$route.query.user_id;
        }
        if(this.$route.query.parentPage) {
            this.parentPage = this.$route.query.parentPage;
        }
        // user is editing an existing design, mark loading state
        // we do this on created so the initial mount render shows the correct state
        if (this.$route.query.landing_id) {
            this.updateData('existingLandingLoading', true);
            this.updateData('existingLandingIdString', this.$route.query.landing_id);
        }
        switch (this.$route.name) {
            case 'LandingBuilder':
                break;
            case 'BuildLanding':
                this.step = 2;
                break;
            case 'FinishLanding':
                // shouldn't allowed to end up on Finish step on page load
                this.changeStep(2);
                break;
        }
    },

    async mounted() {
        await this.getBackgroundImages(); // fetch background images

        // user is editing an existing design, populate the form once mounted
        if (this.$route.query.landing_id) {
            this.getLanding(this.$route.query.landing_id);
        }

        // setup confirmation dialog listener
        // TODO: find a better (more reusable) flow for this
        this.$on('bkgdDeleteConfirmed', this.deleteBackgroundImage);
    },

    methods: {
        changeStep(newStepNumber) {
            switch (newStepNumber) {
                case this.step:
                    return;
                case 1:
                    this.$router.push({ name: 'LandingBuilder' });
                    break;
                case 2:
                    this.$router.push({ name: 'BuildLanding' });
                    break;
                case 3:
                    this.$router.push({ name: 'FinishLanding' });
                    break;
                default:
                    this.$router.push({ name: 'LandingBuilder' });
                    break;
            }

            this.step = newStepNumber;
        },

        updateData(propKey, propValue) {
            this[propKey] = propValue;
        },

        updatePreviewProp(propKey, propValue) {
            this.landingProps[propKey] = propValue;
        },

        /**
         * @param {Object} img - image object, if we're editing
         */
        openBackgroundUploader(img) {
            if (img) {
                // stringify then parse so we copy the object rather than pass by reference
                this.updateData('newBkgdImg', JSON.parse(JSON.stringify(img)));
            } else {
                // log the click on the upload background button
                // if we have an img this was opened by clicking the edit button
                this.reportEvent('Custom Backgrounds - Upload Overlay Opened');
            }
            this.$refs.bgUpload.openDialog();
        },

        setNewBgImg(propKey, imgData) {
            this.newBkgdImg.data = imgData;
        },

        /**
         * Event handler for event emitted by AssetTile for background selection
         * @param {Object} img - Object representing background image
         * @param {Boolean} fromLoad - boolean flag to indicate if this function is being called from a load routine for editing an existing design. Defaults to false.
         */
        selectBackgroundImage(img, fromLoad = false) {
            // mark image as selected
            this.bkgdImages.forEach((i) => {
                i.selected = i.id === img.id;
            });
            this.updatePreviewProp('bkgd_image', img);
            this.updatePreviewProp('bkgd_image_id', img.id);
            // report image selection if not from loading a design, and the user did not choose "no image"
            if (!fromLoad && img.id !== null) {
                this.reportEvent('Custom Backgrounds - Background Selected');
            }
        },

        /**
         * Event handler for event emitted by AssetTile for background deletion
         */
        async deleteBackgroundImage(del = false) {
            if (!del) {
                this.updateData('bkgdImgDeleting', null);
                this.$refs.bgDeleteConfirm.closeDialog();
                return;
            }
            try {
                let response;
                response = await axios.delete(`/api/backgroundImage/${this.uuid}/${this.bkgdImgDeleting.id}`);
                if (response.data && response.data.error) {
                    Bugsnag.notify('Delete Background Error', function(event) {
                        event.addMetadata('metadata', { error: response.data.error});
                    });
                } else if (response.data) {
                    this.updateData('bkgdImgDeleting', null);
                    this.getBackgroundImages(); // update list of images
                    this.$refs.bgDeleteConfirm.closeDialog();
                }
            } catch (e) {
                Bugsnag.notify('Delete Background Exception', function(event) {
                    event.context('User clicked delete button on a background image');
                });
            }
        },

        confirmBkgdDelete(img) {
            this.updateData('bkgdImgDeleting', img);
            this.$refs.bgDeleteConfirm.openDialog();
        },

        resetNewBkgdImg() {
            this.newBkgdImg = {id: null, name: null, data: null, bkgd_image: null};
            this.bkgdImgError = false;
        },

        async getBackgroundImages() {
            let response;
            let images = [{
                id: null,
                noImage: true,
                name: 'No Image',
                selected: null,
            }]; // default to "no image" option
            try {
                response = await axios.get(`/api/backgroundImages/${this.uuid}/${this.existingLandingIdString}`);
                if (response.data) {
                    const fetchedImages = response.data.data.background_images;
                    fetchedImages.forEach(i => i.selected = false);
                    images = images.concat(response.data.data.background_images);
                    this.updateData('bkgdImages', images);
                }
            } catch (e) {
                Bugsnag.notify(e, function(event) {
                    event.context = 'Fetching bg images for Landing Builder';
                });
                this.updateData('bkgdImages', images);
            }
            return response;
        },

        async saveBackgroundImage() {
            // ensure all fields provided
            if (this.$v.newBkgdImg.$invalid) {
                setTimeout(() => {
                    this.bkgdImgError = true;
                });
                this.submittingBkgdImg = false;
                return;
            }

            this.submittingBkgdImg = true;
            this.bkgdImageDuplicateName = false;

            const params = {
                name: this.newBkgdImg.name,
                metadata: {
                    uuid: this.uuid,
                    user_id: this.user_id,
                },
            };
            let apiEndPoint = '/api/backgroundImage';
            // if renaming, all we need is the id
            if (this.editingBkgdImg) {
                params.id = this.newBkgdImg.id;
                apiEndPoint += '/rename';
            } else {
                // if it's a new image, add the image data
                params.bkgdImage = this.newBkgdImg.data;
            }

            try {
                let response;
                response = await axios.post(apiEndPoint, params);
                if (response.data.data && response.data.data.error) {
                    if (response.data.data.error === 'duplicate name') {
                        this.submittingBkgdImg = false;
                        this.bkgdImageDuplicateName = true;
                    } else {
                        Bugsnag.notify('Save Background Error', function(event) {
                            event.addMetadata('metadata', { error: response.data.data.error });
                        });
                    }
                } else if (response.data) {
                    this.submittingBkgdImg = false;
                    this.bkgdImageDuplicateName = false;

                    // if we saved a new image, we got the object back
                    if (response.data.data && response.data.data.background_image) {
                        // add new image object to the list and select it
                        // add at index 1 so "no image" remains the first item
                        this.bkgdImages.splice(1, 0, { ...response.data.data.background_image, selected: false });
                        this.landingProps.bkgd_image_id = response.data.data.background_image.id;
                        // log that a new image was uploaded
                        this.reportEvent('Custom Backgrounds - New Background Uploaded');
                    } else if (response.data.success) {
                        // update the edited image object
                        let updatedImage = this.bkgdImages.find((img) => {
                            return img.id === this.newBkgdImg.id;
                        });
                        updatedImage.name = this.newBkgdImg.name;
                    }
                    this.$refs.bgUpload.closeDialog();
                }
            } catch (e) {
                Bugsnag.notify(e, function(event) {
                    event.context = 'Uploading new bg image in Landing Builder';
                });
            }
        },

        /**
         * Fetches a Landing Page design and loads it into the form when
         * the user selects to edit an existing design.
         * @param {String} idString - A UUID string of a landing page design
         */
        async getLanding(idString) {
            let response;
            try {
                response = await axios.get(`/api/landingPage/${this.uuid}/${idString}`);
                if (response.data.success) {
                    this.loadLanding(response.data.data.landing_page);
                    // don't navigate until form data has loaded
                    this.$nextTick(function () {
                        this.updateData('existingLandingLoading', false);
                        this.changeStep(2);
                    });
                } else {
                    Bugsnag.notify('getLanding Error', function(event) {
                        event.context = 'Fetching landing page to edit';
                        event.addMetadata('metadata', { error: response.data.error});
                    });
                    this.$nextTick(function () {
                        this.updateData('existingLandingLoading', false);
                        this.updateData('landingLoadError', true);
                    });
                }
            } catch (e) {
                this.$nextTick(function () {
                    this.updateData('existingLandingLoading', false);
                    this.updateData('landingLoadError', true);
                });
                Bugsnag.notify(e, function(event) {
                    event.context = 'Fetching existing landing page design';
                });
            }
            return response;
        },

        /**
         * Handles loading a fetched landing design into the form
         * @param {Object} landing - landing design object as returned by the API
         */
        loadLanding(landing) {
            this.updatePreviewProp('name', landing.name);
            this.updatePreviewProp('header_bkgd_color', landing.header_bkgd_color);
            this.updatePreviewProp('header_logo', landing.header_logo);
            this.updatePreviewProp('button_text_color', landing.button_text_color);
            this.updatePreviewProp('button_color', landing.button_color);
            this.updatePreviewProp('button_below_text_color', landing.button_below_text_color);
            this.updatePreviewProp('button_reply_color', landing.button_reply_color);
            this.updatePreviewProp('button_save_color', landing.button_save_color);
            this.updatePreviewProp('button_share_color', landing.button_share_color);
            this.updatePreviewProp('hide_message_gradient', landing.hide_message_gradient || false);
            this.updateData('existingLandingId', landing.id);
            this.updateData('existingLandingIdString', landing.id_string);
            if (landing.background_image) {
                this.selectBackgroundImage(landing.background_image, true);
                // if the current image was deleted, flag it to show error message
                if (landing.background_image.deleted_at !== null) this.bkgdImgDeleted = true;
            }
        },

        /**
         * Processes the build form for submission to the API
         */
        async saveLanding() {
            this.submissionError = false;
            this.submitting = true;

            if (this.$v.landingProps.$invalid) {
                // to trigger watcher in LandingForm
                // if there are still errors after initial submit
                // TODO: might be a better way to do this
                setTimeout(() => {
                    this.submissionError = true;
                });
                this.submitting = false;
                return;
            }

            const params = {
                name: this.landingProps.name,
                header_bkgd_color: this.landingProps.header_bkgd_color,
                header_logo_file: this.landingProps.header_logo_file,
                bkgd_image_id: this.landingProps.bkgd_image_id,
                button_text_color: this.landingProps.button_text_color,
                button_color: this.landingProps.button_color, // cta button color
                button_below_text_color: this.landingProps.button_below_text_color, // secondary buttons text color
                button_reply_color: this.landingProps.button_reply_color,
                button_save_color: this.landingProps.button_save_color,
                button_share_color: this.landingProps.button_share_color,
                cta_text_bkgd: this.landingProps.cta_text_bkgd, // this maps to setting multiple values in the database
                hide_message_gradient: this.landingProps.hide_message_gradient,
                bkgd_anchor_horizontal: 'center',
                bkgd_anchor_vertical: 'top',
                metadata: {
                    uuid: this.uuid,
                    user_id: this.user_id,
                },
            };

            try {
                let response;
                if (this.existingLandingId) {
                    response = await axios.post(
                        `/api/landingPage/${this.existingLandingId}`,
                        params
                    );
                    if (response.data) {
                        this.handleLandingSaveSuccess(response.data.data.landing);
                    }
                } else {
                    response = await axios.post('/api/landingPage', params);
                    this.submitting = false;
                    if (response.data && !response.data.success) {
                        // API returned an error
                        this.submissionError = true;
                        this.submissionErrorMsg = response.data.message;
                    } else if (response.data) {
                        this.handleLandingSaveSuccess(response.data.data.landing);
                    }
                }

            } catch (e) {
                Bugsnag.notify(e, function(event) {
                    event.context = 'Saving landing page design';
                });
                this.submitting = false;
            }
        },

        /**
         * Resets some state variables and handles navigation after a successful save
         * @param {object} landing - landing page design object as returned by the API
         */
        handleLandingSaveSuccess(landing) {
            // if we don't already have a landing ID, this was a newly created page
            // report the event to analytics dashboard
            if (!this.existingLandingId) {
                this.reportEvent('Landing Page Created In Builder');
            }
            if (this.inIframe) {
                window.parent.postMessage('updateLandingPages', '*');
            }

            this.submissionError = false;
            this.submitting = false;
            this.existingLandingSlug = landing.slug;
            this.existingLandingId = landing.id;
            this.existingLandingIdString = landing.id_string;
            this.changeStep(3);
        },

        /**
         * Report events in our custom analytics dashboard
         * @param {String} eventName - event name to log
         * @param {String} eventType - event type, as defined by analytics team (usually 'Feature')
         *
         * TODO: turn this into a reusable Vue mixin that components can use to report their own events
         */
        async reportEvent(eventName, eventType = 'Feature') {
            var params = {
                eventName: eventName,
                eventType: eventType,
                metadata: {
                    user_id: this.user_id,
                },
            };
            try {
                // API doesn't return anything?
                let response;
                response = await axios.post('/api/addLogEvent', params);
            } catch(e) {
                Bugsnag.notify(e, function(event) {
                    event.context = 'Logging analytics event';
                });
            }

        },

        /**
         * Resets the builder to the initial state for creating a new LP design
         */
        newLanding() {
            this.existingLandingIdString = null;
            this.existingLandingPageId = null;
            this.existingLandingSlug = null;
            this.submitting = false;
            this.submissionError = false;
            this.landingProps = defaultLandingProps();
            this.changeStep(1);
        }
    },
};
</script>
