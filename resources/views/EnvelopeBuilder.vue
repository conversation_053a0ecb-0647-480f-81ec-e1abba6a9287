<template>
    <div
        v-if="!showBuilder"
        class="
            width-90
            max-width-720px
            bg-gray-98
            box-shadow
            border-radius-10px
            margin-center margin-t-24px
            md-margin-t-72px
            margin-b-72px
            pad-t-24px pad-r-24px pad-b-24px pad-l-24px
            md-pad-t-48px md-pad-r-48px md-pad-b-48px md-pad-l-48px
        "
    >
        <h1
            class="
                font-serif font-size-48px
                text-align-center
                margin-t-0px margin-b-24px
                md-margin-b-32px
            "
        >
            Get More Envelopes
        </h1>
        <p class="margin-b-24px">
            You have no available envelopes left to create. Contact
            <a
                href="mailto:<EMAIL>"
                target="_blank"
                class="font-blue-48"
            >Customer Success</a
            >
            if you would like to add more envelopes to your portal.
        </p>
    </div>
    <div
        v-else
        class="
            wrapper
            width-90
            max-width-1280px
            bg-gray-98
            box-shadow
            border-radius-10px
            overflow-hidden
            margin-center margin-t-32px margin-b-72px
            wrapper
            grid
        "
        :class="{
            build: step === 2,
            finish: step >= 3,
            'back-flap': currentView === 'backFlap',
        }"
    >
        <step-indicator :step="step" @changeStep="changeStep" />
        <welcome-info
            v-if="step === 1"
            :uuid="uuid"
            :envelopes-available="envelopesAvailable"
        >
            <p
                v-if="!existingEnvelopeLoading && !envelopeLoadError"
                class="margin-t-0px"
            >
                Our envelope builder is quick and easy and will only take a few minutes to complete.
                <span v-if="envelopesAvailable < 100"> You have
                    <strong>{{ envelopesAvailable }}</strong>
                    <span v-if="envelopesAvailable === 1">envelope</span>
                    <span v-else>envelopes</span> available as part of your account’s package.
                </span>
            </p>
            <p
                v-if="!existingEnvelopeLoading && !envelopeLoadError"
                class="margin-t-0px"
            >
                <a
                    href="https://community.thankview.com/t/y4hzs17#examples-of-great-envelope-designs"
                    target="_blank"
                >Click here</a> to check out envelope examples from other ThankView Users.
            </p>
            <p
                v-if="!existingEnvelopeLoading && !envelopeLoadError"
                class="margin-t-0px"
            >
                Feel free to reach out to our Client Support team via our live chat or
                <a
                    href="mailto:<EMAIL>?subject=Envelopes"
                    target="_blank"
                ><EMAIL></a> for any questions you may have.
            </p>
            <p
                v-if="!existingEnvelopeLoading && envelopeLoadError"
                class="text-align-left bg-red-90 font-red-30 border-radius-6px pad-t-8px pad-r-16px pad-b-8px pad-l-16px"
            >
                Hmmm... We couldn't find that envelope. Try selecting it again, or <a href="mailto:<EMAIL>">contact support</a>.
            </p>
            <p
                v-if="existingEnvelopeLoading"
                class="font-size-24px text-align-center"
            >
                Loading your
                <br>
                envelope design...
                <br>
                <i class="fas fa-spinner fa-spin font-size-36px block margin-center margin-t-24px" />
            </p>
        </welcome-info>
        <envelope-form
            v-else-if="step === 2"
            :canvas-props="canvasProps"
            :design-options="designOptions"
            :selected-design="selectedDesign"
            :submission-error="submissionError"
            :valid="$v"
            @updateCanvasProp="updateCanvasProp"
            @zoomImage="zoomImage"
        />
        <finish-info
            v-else-if="step === 3"
            :envelopes-available="envelopesAvailable"
            :envelopes-credited="envelopesCredited"
        >
            <h1 class="font-serif font-size-48px margin-b-0px">Huzzah!</h1>
            <p>Your envelope is now in your portal.</p>
            <p class="margin-t-0px">
                Prior to using this envelope design in a sent campaign, you may
                delete it at any time. Currently, if you’d like to make any
                edits, you will have to delete the envelope and create a new
                one. Stay tuned for the ability to edit.
            </p>
            <p class="margin-t-0px" v-if="envelopesCredited < 1000">
                You have created {{ envelopesCredited - envelopesAvailable }} of
                {{ envelopesCredited }} envelopes.
            </p>
        </finish-info>
        <div
            v-if="step === 1"
            class="
                preview-area
                flex flex-wrap
                justify-content-center
                bg-white
                max-width-100
                overflow-hidden
                pad-t-40px pad-r-40px pad-b-40px pad-l-40px
            "
        >
            <div class="min-width-300px max-width-100 align-self-center">
                <img
                    src="https://builder-assets.thankview.com/img/envelope-builder-welcome.jpg"
                    alt="Illustration of envelope construction site"
                    class="block width-100 max-width-640px"
                />
            </div>
            <step-navigation
                :step="step"
                @changeStep="changeStep"
            />
        </div>
        <envelope-preview
            v-else-if="step === 2"
            :current-view="currentView"
            :processing-images="processingImages"
            :submitting="submitting"
            :canvas-props="canvasProps"
            :selected-design="selectedDesign"
            :image-exports="imageExports"
            :step="step"
            :submission-error="submissionError"
            :valid="$v"
            @changeStep="changeStep"
            @updateData="updateData"
            @updateCanvasProp="updateCanvasProp"
            @updateImageExport="updateImageExport"
            @saveEnvelope="saveEnvelope"
        />
        <envelope-finish
            v-else-if="step >= 3"
            :name="canvasProps.name"
            :image-exports="imageExports"
            :envelopes-available="envelopesAvailable"
            :envelope-slug="existingEnvelopeSlug"
            :step="step"
            :parent-page="parentPage"
            @changeStep="changeStep"
            @resetEnvelope="resetEnvelope"
        />
    </div>
</template>

<script>
import axios from 'axios';
import Bugsnag from '@bugsnag/js';
import StepIndicator from '../components/StepIndicator';
import StepNavigation from '../components/StepNavigation.vue';
import { required, requiredIf, helpers } from 'vuelidate/lib/validators';
const isHex = helpers.regex('isHex', /^#(?:[0-9a-fA-F]{3}){1,2}$/);

const WelcomeInfo = () =>
    import(/* webpackChunkName: "welcomeChunk" */ '../components/WelcomeInfo');
const EnvelopeForm = () =>
    import(/* webpackChunkName: "envelopeChunk" */ '../components/EnvelopeForm');
const EnvelopePreview = () =>
    import(
        /* webpackChunkName: "envelopeChunk" */ '../components/EnvelopePreview'
    );
const FinishInfo = () =>
    import(/* webpackChunkName: "finishChunk" */ '../components/FinishInfo');
const EnvelopeFinish = () =>
    import(/* webpackChunkName: "finishChunk" */ '../components/EnvelopeFinish');

function defaultCanvasProps() {
    return {
        name: '',
        showFrontLogo: false,
        frontLogo: '',
        frontLogoMaxWidth: 454,
        frontLogoMaxHeight: 156,
        showBackLogo: false,
        backLogo: '',
        stampImage: '',
        stampFrameWidth: 213,
        stampFrameHeight: 156,
        stampWidth: 169,
        stampHeight: 108,
        // for stampImg preview in BuildForm
        stampDisplayWidth: 236,
        stampDisplayHeight: 150,
        stampZoomLevel: 1,
        design_id: null,
        primary_color: '',
        secondary_color: '',
        tertiary_color: '',
        envelope_color: '',
        envelope_text_color: '#000000',
        stripe_1_color: '',
        stripe_2_color: '',
        stripe_3_color: '',
        liner_color: '',
        postmark_color: 'none',
        postmark_copy: '',
    };
}

export default {
    components: {
        'step-indicator': StepIndicator,
        'welcome-info': WelcomeInfo,
        'envelope-form': EnvelopeForm,
        'envelope-preview': EnvelopePreview,
        'finish-info': FinishInfo,
        'envelope-finish': EnvelopeFinish,
        'step-navigation': StepNavigation
    },

    data() {
        return {
            tvAppPath: '*',
            uuid: null,
            existingEnvelopeId: null,
            existingEnvelopeSlug: '',
            existingEnvelopeLoading: false,
            envelopeLoadError: false,
            envelopesAvailable: 1,
            envelopesCredited: 1,
            step: 1,
            submissionError: false,
            designOptions: [],
            currentView: 'front',
            processingImages: false,
            submitting: false,
            canvasProps: defaultCanvasProps(),
            imageExports: {
                frontImg: '',
                frontSmallImg: '',
                backImg: '',
                backFlapImg: '',
                flapTopOpenImg: '',
                flapsBottomImg: '',
                flapTopCloseImg: '',
                linerLowerImg: '',
                shareImg: '',
                iconImg: '',
                frontWithTextPreview: '',
            },
        };
    },

    validations: {
        canvasProps: {
            name: {
                required,
            },
            primary_color: {
                required,
                isHex,
            },
            secondary_color: {
                required,
                isHex,
            },
            tertiary_color: {
                isHex,
            },
            envelope_color: {
                required,
                isHex,
            },
            envelope_text_color: {
                required,
                isHex,
            },
            frontLogo: {
                required: requiredIf(function() {
                    return this.canvasProps.showFrontLogo;
                }),
            },
            stripe_1_color: {
                isHex,
                required: requiredIf(function() {
                    return this.selectedDesign.num_of_colors;
                }),
            },
            stripe_2_color: {
                isHex,
                required: requiredIf(function() {
                    return this.selectedDesign.num_of_colors >= 2;
                }),
            },
            stripe_3_color: {
                isHex,
                required: requiredIf(function() {
                    return this.selectedDesign.num_of_colors >= 3;
                }),
            },
            backLogo: {
                required: requiredIf(function() {
                    return this.canvasProps.showBackLogo;
                }),
            },
            liner_color: {
                required,
                isHex,
            },
        },
    },

    computed: {
        selectedDesign() {
            const found = this.designOptions.filter(
                d => d.id === this.canvasProps.design_id
            )[0];
            return found || {};
        },

        inIframe() {
            return window.self !== window.top;
        },

        showBuilder() {
            return this.envelopesAvailable || this.existingEnvelopeId;
        },
    },

    watch: {
        'canvasProps.primary_color': function(newVal, oldVal) {
            if (
                this.$v.canvasProps.primary_color.isHex &&
                (!this.canvasProps.envelope_color ||
                    oldVal === this.canvasProps.envelope_color)
            ) {
                this.canvasProps.envelope_color = newVal;
            }
        },

        'canvasProps.secondary_color': function(newVal) {
            if (this.$v.canvasProps.secondary_color.isHex) {
                this.canvasProps.liner_color =
                    this.canvasProps.liner_color || newVal;

                if (this.selectedDesign.num_of_colors) {
                    this.canvasProps.stripe_1_color =
                        this.canvasProps.stripe_1_color || newVal;
                }

                if (this.selectedDesign.num_of_colors > 2) {
                    this.canvasProps.stripe_3_color =
                        this.canvasProps.stripe_3_color || newVal;
                }
            }
        },

        'canvasProps.tertiary_color': function(newVal) {
            if (
                this.$v.canvasProps.tertiary_color.isHex &&
                this.selectedDesign.num_of_colors > 1
            ) {
                this.canvasProps.stripe_2_color =
                    this.canvasProps.stripe_2_color || newVal;
            }
        },

        selectedDesign: function(newVal) {
            if (newVal.num_of_colors) {
                if (this.$v.canvasProps.secondary_color.isHex) {
                    this.canvasProps.stripe_1_color =
                        this.canvasProps.stripe_1_color ||
                        this.canvasProps.secondary_color;
                    if (this.selectedDesign.num_of_colors > 2) {
                        this.canvasProps.stripe_3_color =
                            this.canvasProps.stripe_3_color ||
                            this.canvasProps.secondary_color;
                    }
                }

                if (
                    this.selectedDesign.num_of_colors > 1 &&
                    this.$v.canvasProps.tertiary_color.isHex
                ) {
                    this.canvasProps.stripe_2_color =
                        this.canvasProps.stripe_2_color ||
                        this.canvasProps.tertiary_color;
                }
            }
        },
    },

    async created() {
        const response = await axios.get('/api/design');
        this.designOptions = response.data.designs;

        if (this.$route.query.slug) {
            this.uuid = this.$route.query.slug;
        } else {
            window.location = 'https://www.thankview.com';
        }

        if (this.$route.query.user_id) {
            this.user_id = this.$route.query.user_id;
        }

        if(this.$route.query.parentPage) {
            this.parentPage = this.$route.query.parentPage;
        }

        if (this.$route.query.envelope_slug) {
            this.updateData('existingEnvelopeLoading', true);
            this.getEnvelope(this.$route.query.envelope_slug);
        }

        switch (this.$route.name) {
            case 'EnvelopeBuilder':
                this.getEnvelopeCount();
                break;
            case 'Build':
                this.step = 2;
                break;
            case 'Finish':
                // shouldn't allowed to end up on Finish step on page load
                this.changeStep(2);
                break;
        }
    },

    methods: {
        changeStep(newStepNumber) {
            switch (newStepNumber) {
                case this.step:
                    return;
                case 1:
                    this.$router.push({ name: 'EnvelopeBuilder' });
                    this.getEnvelopeCount();
                    break;
                case 2:
                    this.$router.push({ name: 'Build' });
                    break;
                case 3:
                    this.$router.push({ name: 'Finish' });
                    this.getEnvelopeCount();
                    break;
                default:
                    this.$router.push({ name: 'EnvelopeBuilder' });
                    break;
            }

            this.step = newStepNumber;
        },

        updateData(propKey, propValue) {
            this[propKey] = propValue;
        },

        updateCanvasProp(propKey, propValue) {
            this.canvasProps[propKey] = propValue;
        },

        resetEnvelope() {
            this.existingEnvelopeId = null;
            this.canvasProps = defaultCanvasProps();
        },

        updateImageExport(imageString, imageValue) {
            this.imageExports[imageString] = imageValue;
        },

        zoomImage(zoomIn) {
            if (zoomIn && this.canvasProps.stampZoomLevel < 3) {
                this.canvasProps.stampZoomLevel += 0.2;
            }

            if (!zoomIn && this.canvasProps.stampZoomLevel >= 1.2) {
                this.canvasProps.stampZoomLevel -= 0.2;
            }

            this.canvasProps.stampZoomLevel =
                Math.round(this.canvasProps.stampZoomLevel * 100) / 100;
        },

        async getEnvelopeCount() {
            const response = await axios.get(
                `/api/envelope/count/${this.uuid}`
            );

            this.envelopesAvailable = Math.max(
                0,
                response.data.data.envelopes_available
            );
            this.envelopesCredited = response.data.data.envelopes_credited;

            if (this.step === 3) {
                if (this.inIframe) {
                    window.parent.postMessage(
                        `envelopesAvailable=${this.envelopesAvailable}`,
                        '*'
                    );
                }
            }

            return response;
        },

        async getEnvelope(envelopeSlug) {

            let response;
            try {
                response = await axios.get(`/api/envelope/${this.uuid}/${envelopeSlug}`);
                if (response.data.success) {

                    const envelope = response.data.data.envelope;

                    this.updateCanvasProp('name', envelope.name);
                    this.updateCanvasProp('primary_color', envelope.primary_color);
                    this.updateCanvasProp('secondary_color', envelope.secondary_color);
                    this.updateCanvasProp('tertiary_color', envelope.tertiary_color);

                    this.updateCanvasProp('envelope_color', envelope.envelope_color);
                    this.updateCanvasProp('envelope_text_color', envelope.envelope_text_color);
                    this.updateCanvasProp('liner_color', envelope.liner_color);
                    this.updateCanvasProp('postmark_copy', envelope.postmark_copy);

                    this.updateCanvasProp('stripe_1_color', envelope.color_1);
                    this.updateCanvasProp('stripe_2_color', envelope.color_2);
                    this.updateCanvasProp('stripe_3_color', envelope.color_3);
                    this.updateCanvasProp('design_id', envelope.design_id);

                    this.updateCanvasProp('showFrontLogo', envelope.has_front_logo);
                    if (envelope.has_front_logo) {
                        this.updateCanvasProp('frontLogo', envelope.front_logo);
                    }

                    this.updateCanvasProp('showBackLogo', envelope.has_back_logo);

                    if (envelope.has_back_logo) {
                        this.updateCanvasProp('backLogo', envelope.back_logo);
                    }

                    if (envelope.has_stamp_image) {
                        this.updateCanvasProp('stampImage', envelope.stamp_image);
                    }

                    this.updateCanvasProp('stampZoomLevel', envelope.stamp_zoom_level);
                    this.updateCanvasProp('postmark_color', envelope.postmark_color);

                    this.updateData('existingEnvelopeId', envelope.id);

                    // don't navigate until form data has loaded
                    this.$nextTick(function () {
                        this.updateData('existingEnvelopeLoading', false);
                        this.changeStep(2);
                    });
                } else {
                    Bugsnag.notify('getEnvelope Error', function(event) {
                        event.context = 'Fetching envelope to edit';
                        event.addMetadata('metadata', { error: response.data.error});
                    });
                    this.$nextTick(function () {
                        this.updateData('existingEnvelopeLoading', false);
                        this.updateData('envelopeLoadError', true);
                    });
                }
            } catch (e) {
                this.$nextTick(function () {
                    this.updateData('existingEnvelopeLoading', false);
                    this.updateData('envelopeLoadError', true);
                });
                Bugsnag.notify(e, function(event) {
                    event.context = 'Fetching existing envelope design';
                });
            }

            return response;
        },

        async saveEnvelope() {
            this.submissionError = false;
            this.submitting = true;

            if (this.$v.$invalid) {
                // to trigger watcher in BuildForm if there
                // if there are still errors after initial submit
                // TODO: might be a better way to do this
                setTimeout(() => {
                    this.submissionError = true;
                });
                this.submitting = false;
                return;
            }

            let color_1 = this.canvasProps.stripe_1_color;
            let color_2 = this.canvasProps.stripe_2_color;
            let color_3 = this.canvasProps.stripe_3_color;

            const params = {
                design: {
                    name: this.canvasProps.name,
                    primary_color: this.canvasProps.primary_color,
                    secondary_color: this.canvasProps.secondary_color,
                    tertiary_color: this.canvasProps.tertiary_color,
                    envelope_color: this.canvasProps.envelope_color,
                    envelope_text_color: this.canvasProps.envelope_text_color,
                    liner_color: this.canvasProps.liner_color,
                    postmark_copy: this.canvasProps.postmark_copy,
                    color_1: color_1,
                    color_2: color_2,
                    color_3: color_3,
                    metadata: JSON.stringify({
                        uuid: this.uuid,
                        user_id: this.user_id,
                    }),
                    design_id: this.canvasProps.design_id,
                    show_front_logo: this.canvasProps.showFrontLogo,
                    front_logo: this.canvasProps.frontLogo,
                    show_back_logo: this.canvasProps.showBackLogo,
                    back_logo: this.canvasProps.backLogo,
                    stamp_image: this.canvasProps.stampImage,
                    stamp_zoom_level: this.canvasProps.stampZoomLevel,
                    postmark_color: this.canvasProps.postmark_color,
                },
                name: this.canvasProps.name,
                front: this.imageExports.frontImg,
                back: this.imageExports.linerLowerImg,
                flaps: this.imageExports.flapsBottomImg,
                'front-small': this.imageExports.frontSmallImg,
                'flap-top-open': this.imageExports.flapTopOpenImg,
                'flap-top-close': this.imageExports.flapTopCloseImg,
                share: this.imageExports.shareImg,
                icon: this.imageExports.iconImg,
            };

            try {
                let response;
                if (this.existingEnvelopeId) {
                    response = await axios.put(
                        `/api/envelope/${this.existingEnvelopeId}`,
                        params
                    );
                    if (response.data) {
                        this.existingEnvelopeSlug =
                            response.data.data.envelope.slug;
                    }
                } else {
                    await this.getEnvelopeCount();
                    if (!this.envelopesAvailable) {
                        return;
                    }

                    response = await axios.post('/api/envelope', params);
                    if (response.data && response.data.error) {
                        if (response.data.data.envelopes_available < 1) {
                            this.envelopesAvailable = 0;
                            return;
                        }
                    } else if (response.data) {
                        this.existingEnvelopeId =
                            response.data.data.envelope.id;

                        this.existingEnvelopeSlug =
                            response.data.data.envelope.slug;
                    }
                }

                if (this.inIframe) {
                    window.parent.postMessage('updateEnvelopes', '*');
                }
                this.changeStep(3);
                this.submitting = false;
            } catch (e) {
                console.log('error', e);
                this.submitting = false;
            }
        },
    },
};
</script>
