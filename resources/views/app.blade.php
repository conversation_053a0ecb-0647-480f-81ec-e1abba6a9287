<!doctype html>
<html class="height-100">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>ThankView Envelope Builder</title>

    <link rel="preconnect" href="{{env('THANKVIEW_ASSETS_PATH', 'https://assets.thankview.com/assets/')}}" crossorigin />
    <link rel="preload" href="build/app.js" as="script" />
    <link rel="stylesheet" href="build/app.css" />

    <script src="https://kit.fontawesome.com/017e94e8cc.js"></script>
    @if (env('APP_ENV') === 'production')
        <script>
            window['_fs_run_in_iframe'] = true;
            window['_fs_debug'] = false;
            window['_fs_host'] = 'fullstory.com';
            window['_fs_script'] = 'edge.fullstory.com/s/fs.js';
            window['_fs_org'] = '5KSXT';
            window['_fs_namespace'] = 'FS';
            (function(m,n,e,t,l,o,g,y){
                if (e in m) {if(m.console && m.console.log) { m.console.log('FullStory namespace conflict. Please set window["_fs_namespace"].');} return;}
                g=m[e]=function(a,b,s){g.q?g.q.push([a,b,s]):g._api(a,b,s);};g.q=[];
                o=n.createElement(t);o.async=1;o.crossOrigin='anonymous';o.src='https://'+_fs_script;
                y=n.getElementsByTagName(t)[0];y.parentNode.insertBefore(o,y);
                g.identify=function(i,v,s){g(l,{uid:i},s);if(v)g(l,v,s)};g.setUserVars=function(v,s){g(l,v,s)};g.event=function(i,v,s){g('event',{n:i,p:v},s)};
                g.anonymize=function(){g.identify(!!0)};
                g.shutdown=function(){g("rec",!1)};g.restart=function(){g("rec",!0)};
                g.log = function(a,b){g("log",[a,b])};
                g.consent=function(a){g("consent",!arguments.length||a)};
                g.identifyAccount=function(i,v){o='account';v=v||{};v.acctId=i;g(o,v)};
                g.clearUserCookie=function(){};
                g._w={};y='XMLHttpRequest';g._w[y]=m[y];y='fetch';g._w[y]=m[y];
                if(m[y])m[y]=function(){return g._w[y].apply(this,arguments)};
                g._v="1.2.0";
            })(window,document,window['_fs_namespace'],'script','user');
        </script>
        <!-- Intercom Chat Iframe-->
        <script>

            function inIframe () {
                try {
                    return window.self !== window.top;
                } catch (e) {
                    return true;
                }
            }

            {{-- Only load intercom when not in iframe - parent frame already has intercom --}}
            if (!inIframe()) {
                var APP_ID = '{{ Config::get("intercom.app_id") }}';

                window.intercomSettings = {
                    app_id: APP_ID
                };
                
                (function(){var w=window;var ic=w.Intercom;if(typeof ic==="function"){ic('reattach_activator');ic('update',w.intercomSettings);}else{var d=document;var i=function(){i.c(arguments);};i.q=[];i.c=function(args){i.q.push(args);};w.Intercom=i;var l=function(){var s=d.createElement('script');s.type='text/javascript';s.async=true;s.src='https://widget.intercom.io/widget/' + APP_ID;var x=d.getElementsByTagName('script')[0];x.parentNode.insertBefore(s,x);};if(w.attachEvent){w.attachEvent('onload',l);}else{w.addEventListener('load',l,false);}}})();
            }
        </script>
    @endif
</head>

<body class="bg-blue-90 font-gray-16 font-sans-serif">
    <div id="app">
        <header class="bg-blue-16 font-white box-shadow text-align-center pad-t-16px pad-b-16px pad-l-24px pad-r-24px md-pad-l-40px md-pad-r-40px flex align-items-center">
            <a v-if="inIframe" @click="closeIframe" class="header__back absolute button btn--ghost font-no-underline none md-block">
                <i class="fa fa-chevron-left fa-chevron-class" aria-hidden="true"></i> Back to <span class="text-capitalize">@{{ parentPage }}</span>
            </a>
            <a v-if="inIframe && $route.name !== 'Build'" @click="closeIframe" class="header__back absolute button btn--ghost font-no-underline block md-none">
                <i class="fa fa-chevron-left fa-chevron-class" aria-hidden="true"></i> Back
            </a>
            <img class="block margin-center max-width-200px" src="{{env('THANKVIEW_ASSETS_PATH', 'https://assets.thankview.com/assets/')}}img/tv-logo-2019-white.svg" alt="ThankView company logo" />
        </header>
        <router-view></router-view>
    </div>
    <div class="app-mobile-warning width-90 max-width-640px bg-gray-98 box-shadow border-radius-10px margin-center margin-t-24px md-margin-t-72px margin-b-72px pad-t-24px pad-r-24px pad-b-24px pad-l-24px md-pad-t-48px md-pad-r-48px md-pad-b-48px md-pad-l-48px">
        <h1 class="font-serif font-size-48px text-align-center margin-t-0px margin-b-24px md-margin-b-32px">Hold up!</h1>
        <p class="margin-b-24px">
            Our quick and easy envelope builder is currently <span class="font-w-7">only available on desktop</span> to provide you with the best experience possible. We’ll see you on desktop!
        </p>
        <p>
            If you need assistance with your custom envelope creation, please feel free to reach out to <a href="mailto:<EMAIL>" class="font-blue-48">Customer Success</a>.
        </p>
    </div>
    <script>
        window.config = {
            appPath: '{{env("APP_PATH")}}',
            env: '{{env("APP_ENV")}}',
            tvMainAppUrl: '{{env("MIX_THANKVIEW_APP_URL")}}',
            bugsnagApiKey: '{{env("BUGSNAG_API_KEY")}}',
            sentryDsn: '{{env("SENTRY_LARAVEL_DSN")}}',
            sentryTracesSampleRate: '{{env("SENTRY_TRACES_SAMPLE_RATE")}}',
            assetsPath: '{{env("ASSETS_PATH", "https://builder-assets.thankview.com/")}}',
            thankviewAssetsPath: '{{env("THANKVIEW_ASSETS_PATH", "https://assets.thankview.com/assets/")}}',
            thankviewS3AssetsPath: '{{env("THANKVIEW_WEST_2_ASSETS_PATH", "https://thankviews.s3.us-west-2.amazonaws.com/")}}',
        };
    </script>
    <script async src="{{ mix('app.js') }}"></script>
</body>

</html>
