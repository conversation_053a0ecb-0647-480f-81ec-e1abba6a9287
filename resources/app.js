import Vue from 'vue';
import VueRouter from 'vue-router';
import Vuelidate from 'vuelidate';

import Bugsnag from '@bugsnag/js';
import BugsnagPluginVue from '@bugsnag/plugin-vue';

import * as Sentry from '@sentry/vue';
import { Integrations } from '@sentry/tracing';

Bugsnag.start({
  apiKey: window.config.bugsnagApiKey,
  plugins: [new BugsnagPluginVue()],
  releaseStage: process.env.NODE_ENV,
});


// loading with import syntax here helps webpack split the app into multiple chunks that can be lazy loaded
// component: EnvelopeBuilder,
// const EnvelopeBuilder = () =>
//     import( /* webpackChunkName: "envelopeBuilderChunk" */ './views/EnvelopeBuilder.vue')
import EnvelopeBuilder from './views/EnvelopeBuilder.vue';
import LandingBuilder from './views/LandingBuilder.vue';

// required for using axios/IE11 polyfill
import 'es6-promise/auto';

// load window config object
Vue.prototype.$config = window.config;

Vue.use(VueRouter);
Vue.use(Vuelidate);


const router = new VueRouter({
    mode: 'history',
    base: '/',
    routes: [
        // TODO: have new component for root to choose envelope or landing builder and move env builder to /envelope
        {
            name: 'EnvelopeBuilder',
            path: '/',
            component: EnvelopeBuilder,
        },
        {
            name: 'Build',
            path: '/build',
            component: EnvelopeBuilder
        },
        {
            name: 'Finish',
            path: '/finish',
            component: EnvelopeBuilder
        },
        {
            name: 'LandingBuilder',
            path: '/landing',
            component: LandingBuilder,
        },
        {
            name: 'BuildLanding',
            path: '/build-landing',
            component: LandingBuilder
        },
        {
            name: 'FinishLanding',
            path: '/finish-landing',
            component: LandingBuilder
        },
    ],
    // data is for any app-wide constants we may need
    // currently unsure how to use this in a component template, or if this is best practice
    data: {
        cdnPath: window.config.thankviewAssetsPath ? window.config.thankviewAssetsPath + 'env_builder' : 'https://assets.thankview.com/assets/env_builder',
    }
});

Sentry.init({
    Vue,
    dsn: window.config.sentryDsn,
    tracesSampleRate: window.config.sentryTracesSampleRate || 1.0,
    environment: process.env.NODE_ENV
  });

new Vue({
    router,
    data () {
        return {
            tvAppPath: '*',
            parentPage: '',
            config: window.config,
        };
    },

    computed: {
        inIframe () {
            return window.self !== window.top;
        }
    },

    mounted () {
        this.parentPage = this.$route.query.parentPage;
    },

    methods: {
        closeIframe () {
            if (this.inIframe) {
                window.parent.postMessage('close', '*');
            } else {
                window.location.href = this.tvAppPath;
            }
        }
    },
}).$mount('#app');

// now that Vue is mounted, install Bugsnag
const bugsnagVue = Bugsnag.getPlugin('vue');
bugsnagVue.installVueErrorHandler(Vue);