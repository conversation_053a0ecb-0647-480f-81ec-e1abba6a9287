<?php

use Illuminate\Http\Request;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. Simply tell Lumen the URIs it should respond to and give it the Closure to call when that URI is requested. Enjoy building your API!
|
*/
$router->group(['prefix' => 'api'], function () use ($router) {
    $router->get('design', ['uses' => 'Api\DesignController@index']);

    $router->post('addLogEvent', ['uses' => 'Api\LogEventsController@addEvent']);

    $router->get('backgroundImages/{uuid}/{landingPageIdString}', ['uses' => 'Api\BackgroundImageController@index']);
    $router->post('backgroundImage', ['uses' => 'Api\BackgroundImageController@store']);
    $router->post('backgroundImage/rename', ['uses' => 'Api\BackgroundImageController@rename']);
    $router->delete('backgroundImage/{uuid}/{id}', ['uses' => 'Api\BackgroundImageController@destroy']);

    $router->get('landingPage/{uuid}/{id}', ['uses' => 'Api\LandingPageController@getLandingPage']);
    $router->get('landingPages/{uuid}', ['uses' => 'Api\LandingPageController@index']);
    $router->get('landingPages/stats/{uuid}', ['uses' => 'Api\LandingPageController@stats']);
    $router->post('landingPage', ['uses' => 'Api\LandingPageController@store']);
    $router->post('landingPage/{id}', ['uses' => 'Api\LandingPageController@update']);

    $router->group(['prefix' => 'envelope'], function () use ($router) {
        $router->get('count/{uuid}', ['uses' => 'Api\EnvelopeController@count']);
        $router->get('/{uuid}/{envelopeSlug}', ['uses' => 'Api\EnvelopeController@getEnvelope']);
        $router->post('/', [
            'middleware' => 'envelope_count', 'uses' => 'Api\EnvelopeController@store',
        ]);
        $router->put('{envelopeId}', ['uses' => 'Api\EnvelopeController@update']);
        $router->post('/delete', ['middleware' => 'api-gateway', 'uses' => 'Api\EnvelopeController@thankviewDelete']);
    });

    $router->group(['prefix' => 'landing'], function () use ($router) {
        $router->get('count/{uuid}', ['uses' => 'Api\LandingPageController@count']);
        $router->post('/', [
            'middleware' => 'landing_count', 'uses' => 'Api\LandingPageController@store',
        ]);
        $router->put('{landingPageId}', ['uses' => 'Api\LandingPageController@update']);
        $router->post('/delete', ['middleware' => 'api-gateway', 'uses' => 'Api\LandingPageController@thankviewDelete']);
    });
});
