<?php
use <PERSON><PERSON>\Lumen\Testing\DatabaseMigrations;
use <PERSON><PERSON>\Lumen\Testing\DatabaseTransactions;
use Tests\utils\TestingDBHelper;
use App\Support\Utilities;

class EnvelopeControllerTest extends TestCase
{
    public function setUp():void
    {
        $this->envelopeSlugs = [];
        parent::setUp();
    }

    /** @test */
    public function store_new_envelope_without_pieces()
    {
        $name = 'Cute Envelope';
        $params['design'] = [
            'name' => $name
        ];

        $res = $this->call('POST', '/api/envelope', $params);
        $this->assertEquals(500, $res->status());
    }

    // /** @test */
    // public function store_new_envelope_with_pieces()
    // {
    //     $helper = new TestingDBHelper();
    //     $name = 'Cute Envelope';
    //     $params = $helper->envelopePieces();
    //     $params['design'] = [
    //         'name' => $name
    //     ];

    //     $res = $this->call('POST', '/api/envelope', $params);
    //     $res = json_decode($res->content(), true);

    //     $envelope = $res['data']['envelope'];

    //     $this->envelopeSlugs[] = $envelope['slug'];
    //     $this->assertEquals($name, $envelope['name']);
    // }

    //merge changes
    public function tearDown():void
    {   
        foreach($this->envelopeSlugs as $slug) {
            $path = public_path() . '/img/envelopes/' . $slug;
            Utilities::deleteFiles($path);
        }
        parent::tearDown();
    }

}
