<?php

namespace Tests\utils;

use Carbon\Carbon;
use App\Envelope;

class TestingDBHelper
{
    public function envelopePieces()
    {   
        $dir_path = base_path() . '/tests/utils/sample-image.png';
        $image = 'data:image/png;base64,' . base64_encode(file_get_contents($dir_path));

        $data = [
            'back' => $image,
            'flap-top-close' => $image,
            'flap-top-open' => $image,
            'flaps' =>  $image,
            'front' =>  $image,
            'front-small' =>  $image,
            'icon' =>  $image,
            'share' =>  $image
        ];
        return $data;
    }
    private function bitmapImage($piece)
    {
        $dir_path = base_path() . '/tests/utils/envelope_pieces';
        return 'data:image/png;base64,' . base64_encode(file_get_contents($dir_path . '/' . $piece . '.png'));
    }
}