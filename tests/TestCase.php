<?php

use \Mockery as Mockery;

use Tests\utils\DatabaseMigrations;
use <PERSON><PERSON>\Lumen\Testing\DatabaseTransactions;

use <PERSON><PERSON>\Lumen\Testing\TestCase as BaseTestCase;

abstract class TestCase extends BaseTestCase
{
    use DatabaseTransactions;
    use DatabaseMigrations;

    protected $baseUrl;

    protected static $migrationsRan = false;

    public function setUp():void
    {
        parent::setUp();

        DB::beginTransaction();
    }
    /**
     * Creates the application.
     *
     * @return \Laravel\Lumen\Application
     */
    public function createApplication()
    {
        return require __DIR__.'/../bootstrap/app.php';
    }
    
    public function tearDown():void
    {
        Mockery::close();
        DB::rollBack();
        parent::tearDown();
    }
}
